<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\School\Assessment\BatchDownloadCompositePdfService;
use App\Services\Tool\MessageService;
use App\Models\School\Assessment\AssessmentBatchDownloadRecord;
use Illuminate\Support\Facades\Log;

class BatchDownloadCompositePdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     *
     * @var int
     */
    public $timeout = 7200; // 增加到1小时

    /**
     * 请求参数
     *
     * @var array
     */
    protected $params;

    /**
     * 创建一个新的任务实例
     *
     * @param array $params
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * 执行任务
     *
     * @param BatchDownloadCompositePdfService $batchDownloadCompositePdfService
     * @return void
     */
    public function handle(BatchDownloadCompositePdfService $batchDownloadCompositePdfService)
    {
        // 增加内存限制到512MB
        ini_set('memory_limit', '512M');
        try {
            Log::info('开始处理综合报告批量下载任务', ['params' => $this->params]);
            
            // 获取用户ID列表和记录名称
            $user_ids = $this->params['user_ids'];
            $record_name = $this->params['record_name'];
            $record_id = $this->params['record_id'];
            $school_id = $this->params['school_id'];
            
            // 创建ZIP文件并获取URL
            $zip_url = $batchDownloadCompositePdfService->createCompositePdfZipArchive($user_ids, $record_name,$school_id);
            
            // 更新下载记录状态
            $this->updateDownloadRecord($record_id, $zip_url);
            
            // 发送成功通知
            $this->sendNotification($this->params['user_id'], $record_name, $zip_url);
            
            Log::info('综合报告批量下载任务完成', ['record_id' => $record_id, 'zip_url' => $zip_url]);
        } catch (\Exception $e) {
            // 发送失败通知
            $this->sendFailureNotification($this->params['user_id'], $record_name, $e->getMessage());
            
            throw new \Exception("综合报告批量下载任务失败", 500, $e);
        }
    }

    /**
     * 更新下载记录状态
     *
     * @param int $record_id
     * @param string $zip_url
     * @return void
     */
    protected function updateDownloadRecord(int $record_id, string $zip_url): void
    {
        AssessmentBatchDownloadRecord::where('id', $record_id)
            ->update([
                'zip_url' => $zip_url,
                'is_completed' => true
            ]);
    }

    /**
     * 发送成功通知
     *
     * @param int $user_id
     * @param string $record_name
     * @param string $zip_url
     * @return void
     */
    protected function sendNotification(int $user_id, string $record_name, string $zip_url): void
    {
        $message = [
            'title' => '综合报告批量下载完成',
            'content' => '测评任务：' . $record_name . ' 综合报告批量下载已完成，您可以点击下载链接获取文件。',
            'type' => 'success',
            'url' => $zip_url
        ];
        
        $messageService = new MessageService();
        $messageService->sendMessage($user_id, $message);
    }

    /**
     * 发送失败通知
     *
     * @param int $user_id
     * @param string $record_name
     * @param string $error_message
     * @return void
     */
    protected function sendFailureNotification(int $user_id, string $record_name, string $error_message): void
    {
        $message = [
            'title' => '综合报告批量下载失败',
            'content' => '测评任务：' . $record_name . ' 综合报告批量下载失败，原因：' . $error_message,
            'type' => 'error',
        ];
        
        $messageService = new MessageService();
        $messageService->sendMessage($user_id, $message);
    }

    /**
     * 任务失败的处理方法
     *
     * @param \Exception $exception
     * @return void
     */
    public function failed(\Exception $exception)
    {
        Log::error('综合报告批量下载任务最终失败', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
            'params' => $this->params
        ]);
        
        // 更新记录状态为失败
        AssessmentBatchDownloadRecord::where('id', $this->params['record_id'])
            ->update([
                'is_completed' => false
            ]);
    }
}