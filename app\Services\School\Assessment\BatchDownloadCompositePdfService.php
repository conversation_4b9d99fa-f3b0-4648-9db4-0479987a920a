<?php
namespace App\Services\School\Assessment;

use Illuminate\Support\Facades\Storage;
use App\Services\CreatePdfFileService;
use App\Services\BaseService;
use App\Models\School\Assessment\AssessmentComprehensivePdfUrl;
use App\Models\User;
use ZipArchive;
use Exception;

class BatchDownloadCompositePdfService extends BaseService
{
    protected PdfGeneratorService $pdfGeneratorService;

    public function __construct(PdfGeneratorService $pdfGeneratorService)
    {
        $this->pdfGeneratorService = $pdfGeneratorService;
    }

    /**
     * 创建综合报告ZIP归档文件
     *
     * @param array $userIds 用户ID数组
     * @param string $recordName 记录名称
     * @return string ZIP文件URL
     * @throws Exception
     */
    public function createCompositePdfZipArchive(array $userIds, string $recordName): string
    {
        $tempDir = storage_path('app/temp/composite_pdf_zip');
        $zipFileName = $this->sanitizeFileName($recordName) . '_' . date('YmdHis') . '.zip';
        $localZipPath = $tempDir . '/' . $zipFileName;

        $this->ensureTempDirectoryExists($tempDir);
        
        $zip = new ZipArchive();
        if ($zip->open($localZipPath, ZipArchive::CREATE) !== TRUE) {
            throw new Exception('无法创建ZIP文件');
        }

        $addedFiles = 0;
        
        // 处理每个用户的综合报告
        foreach ($userIds as $userId) {
            // 确保用户存在
            $user = User::find($userId);
            if (!$user) {
                continue; // 跳过不存在的用户
            }
            
            // 获取或生成综合报告PDF
            $pdfUrl = $this->getOrGenerateCompositePdf($userId);
            if (empty($pdfUrl)) {
                continue; // 跳过无法生成PDF的用户
            }
            
            // 从URL中提取路径
            $remotePath = parse_url($pdfUrl, PHP_URL_PATH);
            if (empty($remotePath)) {
                continue; // 跳过无效URL
            }
            
            $localPdfPath = $tempDir . '/' . basename($remotePath);
            
            // 从SFTP下载PDF
            $fileContent = Storage::disk('sftp_assessment')->get($remotePath);
            if (empty($fileContent)) {
                continue; // 跳过不存在的文件
            }
            
            // 保存到本地临时文件
            file_put_contents($localPdfPath, $fileContent);
            
            // 生成文件名并添加到ZIP
            $filename = $this->generatePdfFileName($user);
            
            // 修复中文文件名乱码问题
            $zip->setArchiveComment('UTF-8');
            $zip->addFile($localPdfPath, $filename);
            
            $addedFiles++;
        }
        
        if ($addedFiles === 0) {
            throw new Exception('没有可用的综合报告PDF文件可以打包');
        }

        $zip->close();

        $remoteZipPath = $this->uploadZipToSftp($localZipPath, $zipFileName);
        
        // 清理临时文件
        $this->cleanupTempFiles($tempDir, $localZipPath);
        
        return 'https://s.yishengya.cn' . $remoteZipPath;
    }

    /**
     * 获取或生成综合报告PDF
     *
     * @param int $userId 用户ID
     * @return string|null PDF URL
     */
    protected function getOrGenerateCompositePdf(int $userId): ?string
    {
        // 检查是否已有综合报告PDF
        $existingPdf = AssessmentComprehensivePdfUrl::where('user_id', $userId)
            ->where('module', 'career')
            ->value('pdf_url');
            
        if ($existingPdf) {
            return $existingPdf;
        }
        
        // 生成新的综合报告PDF
        try {
            return $this->pdfGeneratorService->generateCompositePdf($userId, 'career');
        } catch (\Exception $e) {
            // 记录错误但不中断流程
            \Illuminate\Support\Facades\Log::error('生成用户综合报告失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 生成PDF文件名
     *
     * @param User $user
     * @return string
     */
    protected function generatePdfFileName(User $user): string
    {
        $userName = $user->real_name ?? ('用户_' . $user->id);
        $sanitizedName = $this->sanitizeFileName($userName);
        
        return sprintf('%s_生涯综合报告.pdf', $sanitizedName);
    }

    /**
     * 清理文件名中的非法字符
     *
     * @param string $fileName
     * @return string
     */
    protected function sanitizeFileName(string $fileName): string
    {
        // 移除文件名中的非法字符
        $fileName = preg_replace('/[\/:*?"<>|]/', '_', $fileName);
        // 限制文件名长度
        return mb_substr($fileName, 0, 100);
    }

    /**
     * 确保临时目录存在
     *
     * @param string $tempDir
     * @throws Exception
     */
    protected function ensureTempDirectoryExists(string $tempDir): void
    {
        if (!file_exists($tempDir) && !mkdir($tempDir, 0777, true)) {
            throw new Exception('无法创建临时目录: ' . $tempDir);
        }
    }

    /**
     * 上传ZIP到SFTP
     *
     * @param string $localZipPath
     * @param string $zipFileName
     * @return string 远程路径
     * @throws Exception
     */
    protected function uploadZipToSftp(string $localZipPath, string $zipFileName): string
    {
        $remoteZipPath = '/saas_upload/assessment/composite_pdf_zip/' . $zipFileName;
        
        $zipContent = file_get_contents($localZipPath);
        if (empty($zipContent)) {
            throw new Exception('ZIP文件生成失败或为空');
        }
        
        Storage::disk('sftp_assessment')->put($remoteZipPath, $zipContent);
        
        return $remoteZipPath;
    }

    /**
     * 清理临时文件
     *
     * @param string $tempDir
     * @param string $localZipPath
     */
    protected function cleanupTempFiles(string $tempDir, string $localZipPath): void
    {
        if (file_exists($tempDir)) {
            array_map('unlink', glob($tempDir . '/*.pdf'));
            if (file_exists($localZipPath)) {
                unlink($localZipPath);
            }
        }
    }
}