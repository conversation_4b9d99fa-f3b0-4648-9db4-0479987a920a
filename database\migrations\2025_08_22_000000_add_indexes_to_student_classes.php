<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('student_classes', function (Blueprint $table) {
            // 组合索引以支撑 transferData 查询（按学年和班级过滤 + id 排序）
            $table->index(['school_year', 'class_id', 'deleted_at', 'id'], 'sc_year_class_deleted_id_idx');
            // 如需更常见按学生维度查询最新记录，可解注以下索引
            // $table->index(['student_id', 'school_year', 'deleted_at', 'id'], 'sc_student_year_deleted_id_idx');
        });
    }

    public function down(): void
    {
        Schema::table('student_classes', function (Blueprint $table) {
            $table->dropIndex('sc_year_class_deleted_id_idx');
            // $table->dropIndex('sc_student_year_deleted_id_idx');
        });
    }
};

