<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AioController extends Controller
{
    protected string $table = 'ysy_aio_info';

    protected function conn()
    {
        $connection = config('datasync.sync_connection', 'sync_mysql');
        return DB::connection($connection);
    }

    /**
     * 一体机信息列表（分页 + 可选筛选）
     * GET /api/admin/aio/list
     */
    public function index(Request $request)
    {
        try {
            $perPage = (int) $request->input('per_page', 15);
            $page = (int) $request->input('page', 1);

            $code = $request->input('code');
            $userName = $request->input('user_name');
            $schoolName = $request->input('school_name');

            $query = $this->conn()->table($this->table)
                ->when($code, fn($q) => $q->where('code', 'like', "%{$code}%"))
                ->when($userName, fn($q) => $q->where('user_name', 'like', "%{$userName}%"))
                ->when($schoolName, fn($q) => $q->where('school_name', 'like', "%{$schoolName}%"))
                ->where('step', 0)
                ->orderBy('id', 'desc');

            $list = $query->paginate($perPage, ['*'], 'page', $page);

            return $this->success([
                'list' => $list->items(),
                'total' => $list->total(),
                'current_page' => $list->currentPage(),
                'per_page' => $list->perPage(),
                'last_page' => $list->lastPage(),
                'from' => $list->firstItem(),
                'to' => $list->lastItem(),
            ]);
        } catch (\Exception $e) {
            return $this->message('获取一体机列表失败：' . $e->getMessage(), 200, 'error');
        }
    }
    /**
     * 检查 code 和 user_name 唯一性（创建场景）
     */
    protected function assertUniqueForCreate(array $data)
    {
        if (!empty($data['code'])) {
            $exists = $this->conn()->table($this->table)->where('code', $data['code'])->first();
            if ($exists) {
                throw new \RuntimeException('code已存在');
            }
        }
        if (!empty($data['user_name'])) {
            $exists = $this->conn()->table($this->table)->where('user_name', $data['user_name'])->first();
            if ($exists) {
                throw new \RuntimeException('user_name已存在');
            }
        }
    }

    /**
     * 检查 code 和 user_name 唯一性（更新场景）
     */
    protected function assertUniqueForUpdate(int $id, array $data)
    {
        if (array_key_exists('code', $data)) {
            $exists = $this->conn()->table($this->table)
                ->where('code', $data['code'])
                ->where('id', '!=', $id)
                ->first();
            if ($exists) {
                throw new \RuntimeException('code已存在');
            }
        }
        if (array_key_exists('user_name', $data)) {
            $exists = $this->conn()->table($this->table)
                ->where('user_name', $data['user_name'])
                ->where('id', '!=', $id)
                ->first();
            if ($exists) {
                throw new \RuntimeException('user_name已存在');
            }
        }
    }


    /**
     * 新增一体机信息
     * POST /api/admin/aio
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'code' => 'required|string|max:100',
            'user_name' => 'required|string|max:100',
            'password' => 'required|string|max:255',
            'school_name' => 'nullable|string|max:255',
            'school_logo' => 'nullable|string|max:255',
            'index_page_style' => 'nullable|integer',
            'steam_course_purchased' => 'nullable|integer',
            'step' => 'nullable|integer',
            'create_time' => 'nullable|date',
            'expire_time' => 'nullable|date',
        ], [
            'code.required' => 'code不能为空',
            'user_name.required' => 'user_name不能为空',
            'password.required' => 'password不能为空',
        ]);

        try {
            // 唯一性校验
            $this->assertUniqueForCreate($data);

            $insert = [
                'code' => $data['code'],
                'user_name' => $data['user_name'],
                // 按要求：password 存明文，md5_password 存 MD5
                'password' => $data['password'],
                'md5_password' => md5($data['password']),
                'school_name' => $data['school_name'] ?? null,
                'school_logo' => $data['school_logo'] ?? null,
                'index_page_style' => $data['index_page_style'] ?? 0,
                'steam_course_purchased' => $data['steam_course_purchased'] ?? 0,
                'step' => $data['step'] ?? 0,
                'create_time' => $data['create_time'] ?? now(),
                'expire_time' => $data['expire_time'] ?? null,
            ];

            $id = $this->conn()->table($this->table)->insertGetId($insert);


            return $this->success([
                'id' => $id,
                'data' => $insert,
            ], '创建成功');
        } catch (\RuntimeException $e) {
            // 唯一性冲突等业务错误：HTTP 200 + code=200
            return $this->message($e->getMessage(), 200, 'error');
        } catch (\Exception $e) {
            return $this->message('创建失败：' . $e->getMessage(), 200, 'error');
        }
    }

    /**
     * 更新一体机信息
     * PUT /api/admin/aio/{id}
     */
    public function update(Request $request, int $id)
    {
        $data = $request->validate([
            'code' => 'sometimes|string|max:100',
            'user_name' => 'sometimes|string|max:100',
            'password' => 'sometimes|string|max:255',
            'school_name' => 'sometimes|nullable|string|max:255',
            'school_logo' => 'sometimes|nullable|string|max:255',
            'index_page_style' => 'sometimes|nullable|integer',
            'steam_course_purchased' => 'sometimes|nullable|integer',
            'step' => 'sometimes|nullable|integer',
            'create_time' => 'sometimes|nullable|date',
            'expire_time' => 'sometimes|nullable|date',
        ]);

        try {
            $exists = $this->conn()->table($this->table)->where('id', $id)->first();
            if (!$exists) {
                return $this->message('对象不存在', 200, 'error');
            }

            // 唯一性校验（更新）
            $this->assertUniqueForUpdate($id, $data);

            // 如果更新了 password，同步生成 md5_password
            if (isset($data['password'])) {
                $data['md5_password'] = md5($data['password']);
            }

            $update = array_intersect_key($data, array_flip([
                'code', 'user_name', 'password', 'md5_password', 'school_name', 'school_logo',
                'index_page_style', 'steam_course_purchased', 'step', 'create_time', 'expire_time'
            ]));

            if (empty($update)) {
                return $this->message('无可更新字段', 200, 'error');
            }

            $this->conn()->table($this->table)->where('id', $id)->update($update);

            return $this->message('更新成功');
        } catch (\Exception $e) {
            return $this->message('更新失败：' . $e->getMessage(), 200, 'error');
        }
    }

    /**
     * 一体机详情（仅返回 step=0 的记录）
     * GET /api/admin/aio/{id}
     */
    public function show(int $id)
    {
        try {
            $row = $this->conn()->table($this->table)
                ->where('id', $id)
                ->where('step', 0)
                ->first();
            if (!$row) {
                return $this->message('对象不存在', 200, 'error');
            }
            return $this->success($row);
        } catch (\Exception $e) {
            return $this->message('获取详情失败：' . $e->getMessage(), 200, 'error');
        }
    }

    /**
     * 删除（逻辑删除：step = -1）
     * DELETE /api/admin/aio/{id}
     */
    public function destroy(int $id)
    {
        try {
            $exists = $this->conn()->table($this->table)->where('id', $id)->first();
            if (!$exists) {
                return $this->message('对象不存在', 200, 'error');
            }
            $affected = $this->conn()->table($this->table)->where('id', $id)->update(['step' => -1]);
            if (!$affected) {
                return $this->message('删除失败', 200, 'error');
            }
            return $this->message('删除成功');
        } catch (\Exception $e) {
            return $this->message('删除失败：' . $e->getMessage(), 200, 'error');
        }
    }
}

