<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\StudentFindpass;
use App\Models\StudentFindpassSmslog;
use App\Services\Admin\StudentFindpassService;
use App\Exceptions\BusinessException;
use Illuminate\Http\Request;

class StudentFindpassController extends Controller
{
    protected $studentFindpassService;

    public function __construct(StudentFindpassService $studentFindpassService)
    {
        $this->studentFindpassService = $studentFindpassService;
    }
    /**
     * 获取找回密码申请列表
     * 显示：学生姓名，用户名，学校名称，入学年份，班级，处理状态
     */
    public function index(Request $request)
    {
        try {
            $query = $this->studentFindpassService->getList($request);

            // 分页
            $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);

            $list = $query->select([
                'id',
                'student_name',
                'user_name',
                'school_name',
                'grade_year',
                'class_name',
                'check_status',
                'md5_password',
                'create_time',
                'check_time'
            ])
            ->orderBy('create_time', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

            return $this->success([
                'list' => $list->items(),
                'total' => $list->total(),
                'current_page' => $list->currentPage(),
                'per_page' => $list->perPage(),
                'last_page' => $list->lastPage(),
                'from' => $list->firstItem(),
                'to' => $list->lastItem(),
            ]);
        } catch (\Exception $e) {
            return $this->error('获取列表失败：' . $e->getMessage());
        }
    }

    /**
     * 处理找回密码申请
     * 管理员审核申请，可以通过或拒绝
     */
    public function process(Request $request, $id)
    {
        try {
            // 验证参数
            $request->validate([
                'check_status' => 'required|in:1,-1',
            ], [
                'check_status.required' => '处理状态不能为空',
                'check_status.in' => '处理状态只能是1(通过)或-1(拒绝)',
            ]);

            $adminUserId = $request->user()->id;
            $checkStatus = $request->input('check_status');

            $result = $this->studentFindpassService->processApplication($id, $adminUserId, $checkStatus, $request);

            return $this->success($result);

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->error('处理失败：' . $e->getMessage());
        }
    }

    /**
     * 校验用户并返回学校、班级、入学年份等信息
     * GET /api/admin/student-findpass/validate?user_name=xxx
     */
    public function validateUser(Request $request)
    {
        $request->validate([
            'user_name' => 'required|string'
        ], [
            'user_name.required' => '用户名不能为空'
        ]);

        $username = $request->input('user_name');

        // 通过用户名获取用户信息（连带学生与最近班级）
        $user = \App\Models\User::with(['student.lastClass'])
            ->where('username', $username)
            ->first();

        if (!$user || !$user->student) {
            // 业务错误也返回 code=200
            return $this->message('账号不存在或不是学生账号', 200, 'error');
        }

        $student = $user->student;
        $lastClass = $student->lastClass; // hasOneThrough select 中包含 class_name、school_year

        // 学校名称通过用户的 organization_id -> organizations.model (School) 获取
        $schoolName = null;
        if (!empty($user->organization_id)) {
            $org = \App\Models\Admin\Organization::find($user->organization_id);
            if ($org && $org->model) {
                // 若组织对应的是学校模型，取其名称
                $schoolName = $org->model->name ?? null;
            }
        }

        $className = $lastClass->class_name ?? null;
        $gradeYear = $student->grade_year ?? ($lastClass->school_year ?? null);

        return $this->success([
            'student_name' => $user->real_name,
            'user_name' => $user->username,
            'school_name' => $schoolName,
            'class_name' => $className,
            'grade_year' => $gradeYear,
        ]);
    }




}
