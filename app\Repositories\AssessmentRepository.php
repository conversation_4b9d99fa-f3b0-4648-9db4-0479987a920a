<?php

namespace App\Repositories;

use App\Models\School\Assessment\Question\AssessmentCareerQuestion;
use App\Models\School\Assessment\Question\AssessmentCapabilityQuestion;
use App\Models\School\Assessment\Question\AssessmentCompetencyQuestion;
use App\Models\School\Assessment\Question\AssessmentPsychologyQuestion;
use App\Models\School\Assessment\Question\AssessmentSubjectQuestion;
use App\Models\School\Assessment\Assessment;

class AssessmentRepository
{
    public function getCareerDimensions($assessmentId)
    {
        $dimensions = AssessmentCareerQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getCapabilityDimensions($assessmentId)
    {
        $dimensions = AssessmentCapabilityQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getCompetencyDimensions($assessmentId)
    {
        $dimensions = AssessmentCompetencyQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getPsychologyDimensions($assessmentId)
    {
        $dimensions = AssessmentPsychologyQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getSubjectDimensions($assessmentId)
    {
        $dimensions = AssessmentSubjectQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }
    
    public function getAssessmentName($assessmentId)
    {
        $assessmentName = Assessment::where('id', $assessmentId)->value('name');
        return $assessmentName;
    }

    /**
     * 获取测评数据
     *
     * @param array $assessmentIds 测评ID列表
     * @param int|null $type 测评类型，可为空 1非心理测评 2心理测评
     * @param string $platform 平台类型，teacher表示教务端，student表示学生端
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAssessments(int $schoolId, array $assessmentIds, ?int $type)
    {
        $query = Assessment::
            join('school_assessments', 'school_assessments.assessment_id', '=', 'assessments.id')
            ->whereIn('school_assessments.assessment_id', $assessmentIds)
            ->when($type, function ($query) use ($type) {
                $query->where('type', $type);
            })
            ->where('school_assessments.school_id', $schoolId)
            ->select(['assessments.id', 'assessments.name', 'assessments.category_code', 'assessments.official_name',
                'assessments.icon', 'assessments.introduction_pc', 'school_assessments.is_open_puce_report']);

        $assessments = $query->orderBy('assessment_sort')->get();

        if ($assessments->isEmpty()) return [];

        return $assessments;
    }
}