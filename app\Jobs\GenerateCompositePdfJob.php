<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\School\Assessment\PdfGeneratorService;
use Throwable;
use App\Services\School\Assessment\MajorRecommendationService;
use App\Repositories\AssignmentRepository;
use App\Repositories\StudentRepository;
use App\Models\School\Assessment\AssessmentComprehensiveRecommendMajor;

class GenerateCompositePdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务参数
     *
     * @var array
     */
    protected $params;

    protected $studentRepository;
    protected $assignmentRepository;
    protected $majorRecommendationService;

    /**
     * Create a new job instance.
     *
     * @param array $params
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
        $this->studentRepository = new StudentRepository();
        $this->assignmentRepository = new AssignmentRepository();
        $this->majorRecommendationService = new MajorRecommendationService();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(PdfGeneratorService $pdfGeneratorService)
    {
        try {
            Log::info('开始生成综合报告pdf', ['params' => $this->params]);

            $latest_assignments = $this->latestAssignments($this->params['user_id'], $this->params['school_id']);
            if(empty($latest_assignments)) return;

            if(in_array($this->params['assessment_id'], [2,3,4])){
                $text = $this->generateMajors($latest_assignments);
                if(!$text) return;
                $this->operateDb($this->params['user_id'], $text);
            }
            
            # 生成pdf并自动更新状态
            $pdf_url = $pdfGeneratorService->generateCompositePdf(
                $this->params['user_id'],
                $this->params['module']
            );

            Log::info('综合报告pdf已生成', [
                'pdf_url' => $pdf_url
            ]);
        } catch (\Exception $e) {
            throw new \Exception("pdf生成失败", 500, $e);
        }
    }

    /**
     * 查询测评最新记录
     * 
     * @return array
     */
    public function latestAssignments($user_id, $school_id){
        $assessment_ids = [2,3,4];
        $latest_assignments = $this->assignmentRepository->getLatestAssignments($user_id, $school_id, $assessment_ids);
        // 判断生成pdf的条件是否充足：智能，性格，兴趣三个测评都要完成
        if (count($latest_assignments) < 3) {
            return [];
        }
        return $latest_assignments;
    }

    /**
     * 调用通义千问服务生成推荐专业
     *
     * @return json
     */
    public function generateMajors($latest_assignments)
    {
        $latest_assignments = array_column($latest_assignments, null, 'assessment_id');
        if(!isset($latest_assignments[2]) || !isset($latest_assignments[3]) || !isset($latest_assignments[4])){
            return [];
        }
        $intelligence_result = $latest_assignments[2]['standard_results']['dimensions'];
        array_multisort(array_column($intelligence_result, 'score'), SORT_DESC, $intelligence_result);

        $advantages = array_slice($intelligence_result, 0, $latest_assignments[2]['standard_results']['each_level_count']['advantages']);
        $advantages = implode(',',array_column($advantages,'name'));

        $neutral = array_slice($intelligence_result, 0, $latest_assignments[2]['standard_results']['each_level_count']['neutral']);
        $neutral = implode(',',array_column($neutral,'name'));

        $disadvantages = array_slice($intelligence_result, 0, $latest_assignments[2]['standard_results']['each_level_count']['disadvantages']);
        $disadvantages = implode(',',array_column($disadvantages,'name'));

        $interest = substr($latest_assignments[4]['standard_results']['code'],0,3);
        $personality = $latest_assignments[3]['standard_results']['code'];

        //专业推荐列表，调用通义千问服务生成
        $text = $this->majorRecommendationService->generateMajorRecommendations($interest, $personality, $advantages, $neutral, $disadvantages);
        if(empty($text)) return [];

        return $text;
    }

    /**
     * 录入或更新推荐专业
     *
     * @return void
     */
    public function operateDb($user_id, $text){
        $majors = AssessmentComprehensiveRecommendMajor::where('user_id',$user_id)->get()->toArray();
        if(empty($majors)){
            AssessmentComprehensiveRecommendMajor::create(['user_id'=>$user_id,'majors'=>$text]);
        }else{
            AssessmentComprehensiveRecommendMajor::where('user_id',$user_id)->update(['majors'=>$text]);
        }
    }

    /**
     * 处理失败作业
     */
    public function failed(Throwable $exception): void
    {
        // 向用户发送失败通知等...
    }
}
