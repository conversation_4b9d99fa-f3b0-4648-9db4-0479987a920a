<?php

use App\Http\Controllers\GKOauthController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\School\System\SchoolController;
use App\Http\Controllers\Tool\AreaController;
use App\Http\Controllers\Tool\ImgCodeController;
use App\Http\Controllers\Tool\MessageController;
use App\Http\Controllers\Tool\UploadController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WeChatPlatformController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

//Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//    return $request->user();
//});


// 登录
Route::post('login', [LoginController::class, 'login']);
// 登录（免验证码）
Route::post('login_syyz', [LoginController::class, 'login_no_captcha']);
//重设密码
Route::post('reset_password', [UserController::class, 'resetPasswordNoLogin']);
// 忘记密码验证
Route::post('forgot_password/verify', [UserController::class, 'verifyForgotPassword']);
// 提交找回密码申请
Route::post('forgot_password/submit', [UserController::class, 'submitForgotPasswordRequest']);
// 查询找回密码申请进度
Route::post('forgot_password/status', [UserController::class, 'queryForgotPasswordStatus']);


// 业务类接口
Route::group(['middleware' => ['access_log', 'throttle:500']], function () {
    // 通用权限接口
    require base_path('routes/auth.php');
    // 后台管理端接口
    require base_path('routes/admin.php');
    // 教育局、代理端接口
    require base_path('routes/partner.php');
    // 学校端接口
    require base_path('routes/school.php');
    // 数据同步模块接口
    require base_path('routes/datasync.php');

});

// 微信相关
Route::group(['prefix' => 'wechat', 'middleware' => ['auth.refresh', 'access_log', 'throttle:200']], function () {
    Route::post('unbind', [WeChatPlatformController::class, 'unbind']);
});

// 通用接口
Route::group(['middleware' => ['access_log', 'throttle:100']], function () {
    // 高考网授权
    Route::post('validate_data', [GKOauthController::class, 'validateData']);
    Route::post('validate_ticket', [GKOauthController::class, 'validateTicket']);
    // 无登录状态获取机构及配置信息
    Route::get('orgInfo', [UserController::class, 'getOrganizationInfo']);
});

// 工具类接口
Route::group(['middleware' => ['access_log', 'throttle:200']], function () {
    // 上传文件
    Route::post('upload/image', [UploadController::class, 'uploadImage'])->name('upload_image');
    Route::post('upload/file', [UploadController::class, 'uploadFile'])->name('upload_file');
    // 验证码
    Route::get('captcha', [ImgCodeController::class, 'getCaptcha'])->name('captcha');
    Route::post('checkCaptcha', [ImgCodeController::class, 'checkCaptcha'])->name('checkCaptcha');
    // 省市区
    Route::get('areas', [AreaController::class, 'index']);
    // 学年列表
    Route::get('school_year_list', [SchoolController::class, 'schoolYearList']);
});

// 消息相关路由
Route::group(['prefix' => 'messages', 'middleware' => ['auth.refresh', 'access_log', 'throttle:200']],function () {
    Route::get('/', [MessageController::class, 'index'])->name('message.index');
    Route::get('/unreadMessageNum', [MessageController::class, 'getUnreadMessageNum'])->name('message.unreadMessageNum');
    Route::get('/{id}', [MessageController::class, 'show'])->name('message.show');
    Route::post('/markAsRead', [MessageController::class, 'markAsRead'])->name('message.markAsRead');
    Route::post('/markAllAsRead', [MessageController::class, 'markAllAsRead'])->name('message.markAllAsRead');
    Route::delete('/', [MessageController::class, 'delete'])->name('message.delete');
});

Route::get('/phpinfo', function () {
    echo phpinfo();
});

