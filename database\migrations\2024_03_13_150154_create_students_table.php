<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->string('student_ids',50)->comment('原学生ID');
            $table->integer('user_id')->comment('用户ID');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('school_campus_id')->comment('学校校区ID');
            $table->string('student_name',20)->comment('姓名');
            $table->tinyInteger('gender')->default(1)->comment('性别：1男2女');
            $table->string('student_no',50)->comment('学号');
            $table->string('school_no',50)->comment('学籍号');
            $table->integer('init_grade_id')->comment('初始年级ID(创建账号时初始年级)');
            $table->year('grade_year')->comment('年级年份如：2023代表2023年入学的学生');
            $table->date('date_start')->comment('入学日期');
            $table->date('date_due')->comment('毕业日期');
            $table->softDeletes();
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
//            $table->integer('last_student_class_id')->comment('最后一次绑定的学生班级关系表id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
