<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use App\Models\School\Assessment\AssessmentTaskAssignment;

class AssignmentController extends Controller
{
    /**
     * 获取pdf_url
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPdfUrl()
    {
        $assignment_id = request()->input('assignment_id');

        $result = AssessmentTaskAssignment::where('id',$assignment_id)->value('pdf_url');
        
        return $this->success($result);
    }
}