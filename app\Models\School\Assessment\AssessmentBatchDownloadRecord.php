<?php

namespace App\Models\School\Assessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssessmentBatchDownloadRecord extends Model
{
    use SoftDeletes;

    protected $table = 'assessment_batch_download_records';

    protected $fillable = [
        'name',
        'detail',
        'is_completed',
        'zip_url',
        'creator',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'detail' => 'json',
        'zip_url' => 'json',
        'is_completed' => 'boolean',
    ];

    public function assessment()
    {
        return $this->belongsTo(Assessment::class, 'assessment_id');
    }

    public function assessmentSchedule()
    {
        return $this->belongsTo(AssessmentSchedule::class, 'assessment_schedule_id');
    }
}