<?php

namespace App\Services\School\Assessment;

use App\Models\School\Assessment\AssessmentTask;
use App\Services\School\System\ClassService;
use App\Models\School\System\School;
use App\Models\School\System\Student;
use App\Models\School\System\StudentClass;
use App\Constants\SchoolConstants;
use App\Services\BaseService;
use App\Services\School\Assessment\AssessmentBasicService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Repositories\AssignmentRepository;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;

class AssessmentTeacherService extends BaseService
{
    public function __construct(protected AssessmentBasicService $assessmentBasicService, protected AssignmentRepository $assignmentRepository, protected ClassService $classService)
    {
    }

    /**
     * 获取学生得分情况的查询构建器
     *
     * @param Request $request 请求对象
     * @return Builder 查询构建器
     */
    public function getStudentsScoreBuilder(Request $request): Builder
    {
        $task_id = $request->input('task_id');
        $school_id = $request->user()->organization->model_id;
        $is_complete = $request->input('is_complete');
    
        // 获取任务分配给的所有学生的查询构建器
        $builder = $this->assignmentRepository->getAssignmentsByTaskId($task_id, $school_id);
        
        // 根据完成状态筛选
        if (!empty($is_complete)) {
            if ($is_complete == '已完成') {
                $builder->whereNotNull('standard_results');
            } elseif ($is_complete == '未完成') {
                $builder->whereNull('standard_results');
            }
        }
    
        return $builder;
    }

    /**
     * 处理学生得分数据
     *
     * @param Collection $assignments 学生分配信息集合
     * @param Request $request 请求对象
     * @return Collection 处理后的学生得分信息集合
     */
    public function processStudentsScore(Collection $assignments, Request $request): Collection
    {
        $task_id = $request->input('task_id');
        
        // 获取任务信息
        $task = AssessmentTask::with('assessment:id,name,category_code')->findOrFail($task_id);
        $assessment_id = $task->assessment_id;
        
        // 获取维度信息
        $dimension_names = $this->assessmentBasicService->getDimensionDetails($assessment_id);

        $code_mapping = [];
        if($assessment_id == 3){
            $code_mapping = config('assessment.career.personality_code_mapping');
        }elseif(in_array($assessment_id, [4, 8])){
            $code_mapping = config('assessment.career.interest_code_mapping');
        }

        // 处理每个学生的得分情况
        return $assignments->map(function ($assignment) use ($dimension_names, $assessment_id, $code_mapping) {
            return $this->processStudentScore($assignment, $dimension_names, $assessment_id, $code_mapping);
        })->values(); // 重新索引
    }

    /**
     * 处理单个学生的得分情况
     *
     * @param object $assignment 学生分配信息
     * @param array $dimension_names 维度名称列表
     * @return object 学生得分信息对象
     */
    protected function processStudentScore($assignment, array $dimension_names, int $assessment_id, array $code_mapping)
    {
        // 判断是否完成
        $is_completed = !empty($assignment->standard_results);
        $completed_text = $is_completed ? '已完成' : '未完成';
            
        // 创建基础学生信息
        $student_info = [
            'assignment_id' => $assignment->assignment_id,
            'pdf_url' => $assignment->pdf_url,
            'student_name' => $assignment->student->student_name ?? '',
            'class_name' => $assignment->studentClass->claass->class_name ?? '',
            'grade_name' => $assignment->studentClass->claass->grade->grade_name ?? '',
            'completed' => $completed_text,
            'completion_time' => $assignment->completion_time,
            'duration' => $assignment->duration,
        ];
        
        // 处理维度得分
        if ($is_completed && isset($assignment->standard_results['dimensions'])) {
            $dimensions_data = [];
            
            foreach ($assignment->standard_results['dimensions'] as $dimension) {
                if (empty($dimension['children'])) {
                    $dimensions_data[$dimension['name']] = $dimension['score'] ?? 0;
                } else {
                    foreach ($dimension['children'] as $child) {
                        $dimensions_data[$child['name']] = $child['score'] ?? 0;
                    }
                }
            }
        }
        
        // 设置类型字段名称和默认值
        $type_field = null;
        $type_value = $is_completed ? ($assignment->standard_results['code'] ?? null) : null;
        
        if ($assessment_id == 3) {
            $type_field = '性格类型';
        } elseif (in_array($assessment_id, [4, 8])) {
            $type_field = '兴趣类型';
        }
        
        // 如果有类型字段，设置其值
        if ($type_field) {
            $student_info[$type_field] = $type_value;
        }
        
        // 返回所有维度的得分
        foreach ($dimension_names as $name) {
            $dimension_key = $name;
            $dimension_key .= isset($code_mapping[$name]) ? '（'.$code_mapping[$name].'）' : '';
            $student_info[$dimension_key] = $is_completed ? ($dimensions_data[$name] ?? 0) : null;
        }

        return (object)$student_info;
    }

    /**
     * 学生的综合报告完成情况列表
     *
     * @param int school_campus_id 校区ID
     * @param int grade_id 年级ID
     * @param int class_id 班级ID
     * @param string student_name 学生姓名
     * @param int school_year 学年
     * @return object 学生综合报告完成情况列表
     */
    public function listBuilder(Request $request)
    {
        // 获取当前登录用户所属学校id
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request->input('school_campus_id');
        $grade_id = $request->input('grade_id');
        $class_id = $request->input('class_id');
        $student_name = $request->input('student_name');
        $school_year = $request->input('school_year');

        // 检查学校是否在上海，以便使用正确的年级映射
        $school = School::find($school_id);
        $isShanghai = SchoolConstants::isShanghai($school->province);

        // 构建基础查询
        $query = Student::where('students.school_id', $school_id)
        ->when($school_campus_id, fn($query) => $query->where('students.school_campus_id', $school_campus_id))
        ->when($student_name, fn($query) => $query->where('students.student_name', 'like', "%$student_name%"));

        // 应用年级筛选 - 使用子查询获取最新的学生班级关系
        if ($grade_id || $class_id || $school_year) {
            $query->whereExists(function ($query) use ($grade_id, $class_id, $school_year) {
                // 确保每个学生只关联到最新的班级记录 根据student_classes表中的id字段进行降序排序 并取第一个结果，即最新的班级
                $query->select(DB::raw(1))
                    ->from('student_classes')
                    ->join('classes', 'student_classes.class_id', '=', 'classes.id')
                    ->whereColumn('student_classes.student_id', 'students.id')
                    ->orderBy('student_classes.id', 'desc')
                    ->limit(1);

                if ($grade_id) {
                    $query->where('classes.grade_id', $grade_id);
                }
                if ($class_id) {
                    $query->where('classes.id', $class_id);
                }
                if ($school_year) {
                    $query->where('student_classes.school_year', $school_year);
                }
            });
        }

        // 预加载关联数据
        $query->with([
            'school:id,name',
            'schoolCampus:id,campus_name',
            'user:id,username,gender',
            // 'lastClass.grade' 
            'lastClass' => function ($query) use ($isShanghai) {
                // 已经在模型中定义了排序和限制，这里只需要加载年级关系
                $query->with(['grade' => function ($query) use ($isShanghai) {
                    if ($isShanghai) {
                        $query->select('id', 'alisa_name as grade_name');
                    } else {
                        $query->select('id', 'grade_name');
                    }
                }]);
            },
            // 添加对assessment_task_assignments表的关联查询
            'assessmentTaskAssignments' => function ($query) {
                $query->whereIn('assessment_id', [2, 3, 4])
                    ->whereNotNull('standard_results')
                    ->select('id', 'student_id', 'assessment_id', 'standard_results');
                    // 只要满足assessment_id在[2,3,4]中且standard_results不为空即可
            },
            // 添加对assessment_comprehensive_pdf_urls表的关联查询
            'comprehensivePdfUrl' => function ($query) {
                // 不需要再次select，因为已经在模型关系中指定了
            }
        ]);

        // 添加status字段
        $query->addSelect([
            'status' => function ($query) {
                $query->selectRaw('CASE WHEN COUNT(*) = 3 THEN 1 ELSE 0 END')
                    ->from('assessment_task_assignments')
                    ->whereColumn('assessment_task_assignments.student_id', 'students.id')
                    ->whereIn('assessment_task_assignments.assessment_id', [2, 3, 4])
                    ->whereNotNull('assessment_task_assignments.standard_results');
            }
        ]);

        $query->whereRaw('(SELECT CASE WHEN COUNT(*) = 3 THEN 1 ELSE 0 END FROM assessment_task_assignments WHERE assessment_task_assignments.student_id = students.id AND assessment_task_assignments.assessment_id IN (2, 3, 4) AND assessment_task_assignments.standard_results IS NOT NULL) = 1');
        
        return $query;
    }
}