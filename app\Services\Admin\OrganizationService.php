<?php

namespace App\Services\Admin;

use App\Exceptions\BusinessException;
use App\Models\Admin\Organization;
use App\Models\Admin\OrganizationHasMenu;
use App\Models\Partner\Partner;
use App\Models\School\System\School;
use App\Services\BaseService;
use App\Services\RoleService;
use App\Services\School\System\SchoolService;
use App\Services\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrganizationService extends BaseService
{
    protected $roleService;
    protected $userService;
    protected  $schoolService;
    protected $menuService;

    // 构造函数
    public function __construct(RoleService $roleService, UserService $userService, SchoolService $schoolService,MenuService $menuService )
    {
        $this->roleService = $roleService;
        $this->userService = $userService;
        $this->schoolService = $schoolService;
        $this->menuService = $menuService;
    }

    public function schoolListBuilder(Request $request){
        $name = $request->input('name');

        return School::query()->with('organization')
            ->when($name, fn($query) => $query->where('name', 'like', "%$name%"));
    }

    public function partnerListBuilder(Request $request){
        $name = $request->input('name');

        return Partner::query()->with('organization')
            ->when($name, fn($query) => $query->where('name', 'like', "%$name%"));
    }

    /**
     * 创建机构默认信息
     * 学校：虚拟机构、角色（教务、老师、学生）、默认主校区
     * 教育局：虚拟机构、角色（管理员）
     */
    public function createOrgDefaultInfo(Request $request, $alias)
    {
        try {
            DB::beginTransaction();

            $real_name = $request->user()->real_name;
            if($alias=='school'){
                $data = filterRequestData('schools');
                $data['creator'] = $real_name;
                $data['updater'] = $real_name;
                $record = School::forceCreate($data);
            } else if($alias=='partner'){
                $data = filterRequestData('partners');
                $data['creator'] = $real_name;
                $data['updater'] = $real_name;
                $record = Partner::forceCreate($data);
            }

            // 创建虚拟机构表记录
            $organization = $this->createOrganization(['model_id' => $record->id, 'model_type' => $alias, 'org_name' => $record->name], $real_name);

            if($alias=='school'){
                // 创建学校默认角色 教务 老师 学生
                $this->roleService->createRole(['name' => '教务', 'type' => 2], $organization->id, $real_name);
                $this->roleService->createRole(['name' => '老师', 'type' => 3], $organization->id, $real_name);
                $this->roleService->createRole(['name' => '学生', 'type' => 1], $organization->id, $real_name);

                // 创建默认主校区
                $this->schoolService->createSchoolCampus(['campus_name' => $record->name . '主校区', 'school_id' => $record->id, 'type' => 3, 'is_main' => 1, 'status' => 1], $real_name);
            } else if($alias=='partner'){
                // 创建默认角色
                $this->roleService->createRole(['name' => '管理员', 'type' => 4], $organization->id, $real_name);
            }

            DB::commit();

            return $record;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("创建机构默认信息逻辑错误", 500, $e->getMessage());
        }
    }

    public function createOrganization($data, $real_name)
    {
        $data['creator'] = $real_name;
        $data['updater'] = $real_name;
        return Organization::forceCreate($data);
    }

    // 获取机构初始菜单
    public function getOrgInitMenus($organization_id)
    {
        $result = OrganizationHasMenu::join('menus', 'menus.id', '=', 'organization_has_menus.menu_id')
            ->select('organization_has_menus.id', 'organization_has_menus.menu_id', 'organization_has_menus.parent_id',
                'organization_has_menus.date_start','organization_has_menus.date_due',
                'organization_has_menus.menu_alias','organization_has_menus.sort','organization_has_menus.creator','organization_has_menus.updater', 'menus.menu_name', 'menus.crowd')
            ->where('menus.status', 1) // 基础菜单表状态正常的菜单
            ->where('organization_has_menus.status', 1) // 已购拥有菜单表状态正常的菜单
            ->where('organization_has_menus.organization_id', $organization_id)
            ->orderBy('organization_has_menus.sort', 'asc')
            ->get()
            ->map(function ($item) {
                // 处理 crowd 字段，确保它是 JSON 格式
                $item->crowd = empty($item->crowd) ? [] : json_decode($item->crowd, true);
                return $item;
            });

            // 获取菜单树形结构
        $menu_list = $this->menuService->getTreeMenus($result);
        return $menu_list;
    }

    // 设置机构菜单
    public function setOrganizationHasMenus(Request $request, $organization_id)
    {
        try {
            DB::beginTransaction();
            
            $menu_data = $request->input('menu_data', []);

            // 获取当前机构已存在的菜单
            $existingMenus = OrganizationHasMenu::where('organization_id', $organization_id)->get();
            $organization = Organization::with('model')->find($organization_id);
            $date_start = $organization->model->date_start;
            $date_due = $organization->model->date_due;
    
            // 获取新设置购买的 menu_id
            $newMenuIds = collect($menu_data)->pluck('menu_id')->toArray();
    
            // 删除原来有现在没有的菜单
            foreach ($existingMenus as $existingMenu) {
                if (!in_array($existingMenu->menu_id, $newMenuIds)) {
                    // 如果数据库中的记录没有出现在请求中，删除该记录
                    $existingMenu->delete();
                }
            }
    
            // 遍历传递过来的菜单数据
            $insertData = [];
            $real_name = $request->user()->real_name;
            foreach ($menu_data as $menu) {
                // 检查是否已经存在该菜单
                $existingMenu = $existingMenus->firstWhere('menu_id', $menu['menu_id']);
    
                // 如果当前菜单数据在数据库中不存在，则插入
                if (!$existingMenu) {
                    $insertData[] = [
                        'organization_id' => $organization_id,
                        'menu_id' => $menu['menu_id'],
                        'parent_id' => $menu['parent_id'],
                        'sort' => $menu['sort'] ?? 1,               // 默认值为1
                        'date_start' => $date_start ?? null,        // 默认值为伙伴的日期
                        'date_due' => $date_due ?? null,            // 默认值为伙伴的日期
                        'status' => $menu['status'] ?? 1,           // 默认状态为启用
                        'creator' => $real_name,
                        'updater' => $real_name,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
    //            else {
    //                // 如果菜单存在，但数据需要更新（例如 status 或 sort 等字段变化）
    //                $existingMenu->update([
    ////                    'parent_id' => $menu['parent_id'],
    ////                    'sort' => $menu['sort'] ?? 1,
    //                    'status' => $menu['status'] ?? 1,
    //                    'updater' => $real_name,
    //                    'updated_at' => now(),
    //                ]);
    //            }
            }
    
            // 批量插入新的菜单数据
            if (count($insertData) > 0) {
                foreach ($insertData as $data) {
                    OrganizationHasMenu::create($data);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("设置机构菜单逻辑错误", 500, $e->getMessage());
        }
    }


    //updateMenuAlias
    public function updateMenuAlias(Request $request)
    {
        $id = $request->input('id');
        $menu_alias = $request->input('menu_alias');
        $date_start = $request->input('date_start');
        $date_due = $request->input('date_due');

        $menu = OrganizationHasMenu::find($id);
        if (!$menu) {
            $this->throwBusinessException('菜单不存在');
        }

        // 检查日期是否发生变化
        $date_start_changed = $menu->date_start != $date_start;
        $date_due_changed = $menu->date_due != $date_due;

        try {
            DB::beginTransaction();

            // 更新当前菜单
            $menu->menu_alias = $menu_alias;
            $menu->date_start = $date_start;
            $menu->date_due = $date_due;
            $menu->save();

            // 如果日期发生变化，更新所有子菜单的日期
            // if ($date_start_changed || $date_due_changed) {
            //     $this->updateChildMenuDates($menu->organization_id, $menu->menu_id, $date_start, $date_due, $date_start_changed, $date_due_changed);
            // }

            DB::commit();
            return $menu;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("更新菜单别名逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 递归更新子菜单的日期
     * @param int $organization_id 机构ID
     * @param int $parent_menu_id 父菜单ID
     * @param string $date_start 开始日期
     * @param string $date_due 结束日期
     * @param bool $update_start 是否更新开始日期
     * @param bool $update_due 是否更新结束日期
     */
    private function updateChildMenuDates($organization_id, $parent_menu_id, $date_start, $date_due, $update_start, $update_due)
    {
        // 查找直接子菜单
        $childMenus = OrganizationHasMenu::where('organization_id', $organization_id)
            ->where('parent_id', $parent_menu_id)
            ->where('status', 1)
            ->get();

        foreach ($childMenus as $childMenu) {
            // 准备更新数据
            $updateData = [];
            if ($update_start) {
                $updateData['date_start'] = $date_start;
            }
            if ($update_due) {
                $updateData['date_due'] = $date_due;
            }

            // 更新子菜单
            if (!empty($updateData)) {
                $childMenu->update($updateData);
            }

            // 递归更新子菜单的子菜单
            $this->updateChildMenuDates($organization_id, $childMenu->menu_id, $date_start, $date_due, $update_start, $update_due);
        }
    }

}
