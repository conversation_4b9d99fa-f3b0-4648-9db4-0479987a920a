<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_classes', function (Blueprint $table) {
            $table->id();
            $table->integer('old_student_id')->comment('老表的学生ID');
            $table->integer('student_id')->comment('学生ID');
            $table->integer('class_id')->comment('班级ID');
            $table->string('class_name',20)->comment('班级名称');
            $table->year('school_year')->comment('学年：指学生的学习年份9月');
            $table->softDeletes();
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
            $table->string('remark')->nullable()->comment('数据备注（批量升级还是手动录入等）');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_classes');
    }
};
