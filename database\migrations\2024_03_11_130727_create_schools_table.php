<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schools', function (Blueprint $table) {
            $table->id();
            $table->string('name',100)->comment('学校名称');
            $table->string('province',20)->nullable()->comment('省份');
            $table->string('city',20)->nullable()->comment('城市');
            $table->string('district',20)->nullable()->comment('区县');
            $table->string('address',255)->nullable()->comment('学校地址');
            $table->date('date_start')->nullable()->comment('启用日期');
            $table->date('date_due')->nullable()->comment('到期日期');
            $table->bigInteger('site_config_id')->nullable()->comment('个性化配置表ID');
            $table->tinyInteger('status')->default(1)->comment('状态1启用2禁用');
            $table->tinyInteger('is_link_gk')->default(0)->comment('是否能跳转第一高考网：0 不可以跳转 1 可以跳转');
            $table->softDeletes();
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
            $table->tinyInteger('yizhan');
            $table->tinyInteger('auto_up_student_grade')->default(1)->comment('是否自动升级学生年级：1启用自动升级 2不启用自动升级');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schools');
    }
};
