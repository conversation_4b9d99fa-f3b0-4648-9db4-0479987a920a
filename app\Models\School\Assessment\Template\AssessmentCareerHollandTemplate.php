<?php

namespace App\Models\School\Assessment\Template;

use App\Models\BaseModel;
use App\Traits\ModelChangeLogTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 霍兰德职业兴趣测评模板模型
 * 
 * 用于管理霍兰德职业兴趣测评的模板数据
 * 
 * @property int $id 主键ID
 * @property string $code 模板代码
 * @property string $type 模板类型
 * @property int $grade_type 适用年级类别(1初中 2高中)
 * @property string $content 模板内容（JSON格式）
 * @property string|null $appellation 称号
 * @property int|null $lowest_score 最低分
 * @property int|null $highest_score 最高分
 * @property string|null $level 等级(高 中 底)
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 */
class AssessmentCareerHollandTemplate extends BaseModel
{
    use HasFactory;
    use SoftDeletes, ModelChangeLogTrait;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'assessment_career_holland_templates';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'type',
        'grade_type',
        'content',
        'appellation',
        'lowest_score',
        'highest_score',
        'level',
    ];

    /**
     * 应该被转换的属性
     *
     * @var array
     */
    protected $casts = [
        'grade_type' => 'integer',
        'lowest_score' => 'integer',
        'highest_score' => 'integer',
    ];

    /**
     * 根据模板代码查询
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCode($query, $code)
    {
        return $query->where('code', $code);
    }

    /**
     * 根据模板类型查询
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 根据年级类型查询
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $gradeType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByGradeType($query, $gradeType)
    {
        return $query->where('grade_type', $gradeType);
    }
}
