<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use App\Models\User;
use App\Models\School\Assessment\AssessmentComprehensivePdfUrl;
use App\Traits\ModelChangeLogTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class Student extends BaseModel
{
    use SoftDeletes, ModelChangeLogTrait;

    // 归属于学校
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }

    // 归属于校区
    public function schoolCampus()
    {
        return $this->belongsTo(SchoolCampus::class, 'school_campus_id', 'id');
    }


    // 对应一个用户
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // 最后一个学生班级关系
   public function lastStudentClass()
   {
    return $this->hasOne(StudentClass::class, 'student_id', 'id')
        ->whereNull('deleted_at')
        ->latest('id');
   }

    // 学生最新班级 根据student_classes表中的id字段进行降序排序 并取第一个结果，即最新的班级
    public function lastClass()
    {
        return $this->hasOneThrough(
            Claass::class, // 最终要获取数据的表
            StudentClass::class, // 中间表
            'student_id', // StudentClass表中关联Student的外键
            'id', // Claass表中的主键
            'id', // Student表中的主键
            'class_id' // StudentClass表中关联Claass的外键
        )
        ->whereNull('student_classes.deleted_at')
        ->orderBy('student_classes.id', 'desc')
        ->select('classes.id as class_id', 'classes.class_name', 'classes.grade_id', 'student_classes.school_year', 'student_classes.id as student_class_id');
    }

    // 学生所有班级记录
    public function studentClasses()
    {
        return $this->hasMany(StudentClass::class, 'student_id', 'id')
            ->whereNull('deleted_at')
            ->orderBy('id', 'desc');
    }

    // 学生所有班级记录（包含班级和年级信息）
    public function classRecords()
    {
        return $this->hasManyThrough(
            Claass::class, // 最终要获取数据的表
            StudentClass::class, // 中间表
            'student_id', // StudentClass表中关联Student的外键
            'id', // Claass表中的主键
            'id', // Student表中的主键
            'class_id' // StudentClass表中关联Claass的外键
        )
        ->whereNull('student_classes.deleted_at')
        ->orderBy('student_classes.id', 'desc')
        ->select('classes.id as class_id', 'classes.class_name', 'classes.grade_id', 'student_classes.school_year', 'student_classes.id as student_class_id', 'student_classes.created_at as join_date');
    }

    //获取学生的所有测评任务分配
    public function assessmentTaskAssignments()
    {
        return $this->hasMany(\App\Models\School\Assessment\AssessmentTaskAssignment::class, 'student_id', 'id');
    }
    
    // 获取学生的综合报告PDF URL
    public function comprehensivePdfUrl()
    {
        return $this->hasOneThrough(
            AssessmentComprehensivePdfUrl::class,
            User::class,
            'id', // User表中的主键
            'user_id', // assessment_comprehensive_pdf_urls表中关联User的外键
            'user_id', // Student表中关联User的外键
            'id' // User表中的主键
        )
        ->select('assessment_comprehensive_pdf_urls.pdf_url')
        ->where('module', 'career');
    }
}
