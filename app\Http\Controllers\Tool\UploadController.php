<?php

namespace App\Http\Controllers\Tool;

use App\Http\Controllers\Controller;
use App\Services\Tool\UploadService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UploadController extends Controller
{
    protected $uploadService;

    // 通过依赖注入注入 User 模型
    public function __construct(UploadService $uploadService)
    {
        $this->uploadService = $uploadService;
    }

    public function uploadImage(Request $request): JsonResponse
    {
        $data = $this->uploadService->uploadFileToSftp($request);
        return $this->success($data);
    }

    public function uploadFile(Request $request): JsonResponse
    {
        $data = $this->uploadService->uploadFileToSftp($request, 'files');
        return $this->success($data);
    }


}
