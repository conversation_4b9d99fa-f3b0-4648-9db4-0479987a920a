<?php

namespace App\Services;

use App\Constants\RedisKeyConstants;
use App\Enums\SystemRoleTypeEnum;
use App\Exceptions\BusinessException;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\ModifyPasswordRequest;
use App\Models\Admin\Organization;
use App\Models\Admin\OrganizationHasMenu;
use App\Models\StudentFindpass;
use App\Models\User;
use App\Services\Admin\MenuService;
use App\Services\Tool\MessageService;
use App\Traits\DingDingMessage;
use App\Traits\LogsLoginAttempts;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Constants\ProvinceConst;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Facades\JWTAuth;
use App\Http\Requests\UserRequest;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Redis;

class UserService extends BaseService
{
    use LogsLoginAttempts;
    use DingDingMessage;

    protected $menuService;

    // 构造函数
    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }


    public function login(LoginRequest $request)
    {
        $captcha = $request->input('captcha'); //验证码
        $key = $request->input('key');
        // 调用captcha_api_check方法。
        if (!captcha_api_check($captcha , $key, 'math')){
            return $this->error('验证码错误或已过期');
        }

        return $this->login_no_captcha($request);
    }

    public function login_no_captcha(Request $request)
    {
        // 接收参数
        $credentials = $request->only('username', 'password');
        $username = $credentials['username'];
        $password = $credentials['password'];
    
        // 检查账号是否被锁定
        $lockKey = keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_LOCK_ACCOUNT'], $username);
        if (Redis::exists($lockKey)) {
            return $this->failLoginWithLog($request, '您的账号登录错误次数过多，已被锁定15分钟，请稍后再试！', 504);
        }
    
        // 查找用户
        $user = User::where('username', $username)->first();
        if (!$user) {
            return $this->handleLoginError($request, $username, '账号错误，请重新输入！');
        }
    
        // 验证密码
        if (!$this->validatePassword($user, $password, $credentials)) {
            return $this->handleLoginError($request, $username, '密码错误，请重新输入！');
        }
    
        // 检查用户状态
        if ($user->status !== 1) {
            return $this->failLoginWithLog($request, '账号已被禁用', 501);
        }
    
        // 检查所属机构状态
        $organization = Organization::with('model')->find($user->organization_id);
        if ($organization && $organization->model->status !== 1) {
            return $this->failLoginWithLog($request, '用户所属学校或机构已被禁用', 501);
        }
    
        // 检查密码强度
        if (!$this->isPasswordStrong($password)) {
            return $this->failLoginWithLog($request, '密码强度不足，请更新为更复杂的密码！', 502);
        }
    
        // 设置用户角色类型
        $user->role_types = $this->getUserRoleTypes();
        // 设置用户所在省份
        $this->setUserProvince($user, $organization);
        // 记录登录成功日志
        $this->logLoginSuccess($request, $user);
    
        $token = Auth::attempt($credentials);
        return $this->respondWithToken($token, $user);
    }
    
    private function handleLoginError(Request $request, string $username, string $message)
    {
        $this->loginErrorLock($request);
        $loginErrorCount = Redis::get(keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_ERROR_COUNT'], $username));
        $leftCount = RedisKeyConstants::SYSTEM['MAX_ERROR_COUNT'] - $loginErrorCount;
    
        if ($loginErrorCount <= 2) {
            return $this->failLoginWithLog($request, $message, 501);
        }
        if ($loginErrorCount == 3) {
            return $this->failLoginWithLog($request, $message . ' 您还有' . $leftCount . '次机会。', 503);
        }
        if ($loginErrorCount == 4) {
            return $this->failLoginWithLog($request, $message . ' 您还有' . $leftCount . '次机会，如果再次输错，您的账户将被锁定。', 503);
        }
        if ($loginErrorCount >= 5) {
            return $this->failLoginWithLog($request, '您的账号登录错误次数过多，已被锁定15分钟，请稍后再试！', 504);
        }

        return $this->failLoginWithLog($request, $message, 501);
    }
    
    private function validatePassword(User $user, string $password, array $credentials): bool
    {
        if ($user->password) {
            return Auth::attempt($credentials);
        }
    
        if (md5($password) === $user->md5_password) {
            $user->password = bcrypt($password);
            $user->save();
            return true;
        }
    
        return false;
    }
    
    private function isPasswordStrong(string $password): bool
    {
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/', $password);
    }
    
    private function setUserProvince(User $user, ?Organization $organization): void
    {
        $province = $organization->model->province ?? '';
        $province = str_replace(['省', '市'], '', $province);
    
        if (!empty(ProvinceConst::$sfMapByName[$province])) {
            $user->province_name = $province;
            $user->province_id = ProvinceConst::$sfMapByName[$province]['id'];
        }
    }

    public function login_no_captcha1(Request $request)
    {
        // 接收参数
        $credentials = $request->only('username', 'password');
        $username = $credentials['username'];
        $password = $credentials['password'];

        $key = keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_LOCK_ACCOUNT'], $username);
        if (Redis::exists($key)) {
            return $this->failLoginWithLog($request, '您的账号登录错误次数过多，已被锁定15分钟，请稍后再试！',504);
        }

        // 查找用户
        $user = User::where('username', $username)->first();
        if (!$user) {
            $this->loginErrorLock($request);
            // 登录错误次数
            $login_error_count = Redis::get(keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_ERROR_COUNT'], $username));
            // 剩余次数
            $left_count = RedisKeyConstants::SYSTEM['MAX_ERROR_COUNT'] - $login_error_count;
            if ($login_error_count == 3) {
                return $this->failLoginWithLog($request, '账号错误，请谨慎确认，您还有'. $left_count .'次机会。', 503);
            }
            if ($login_error_count == 4) {
                return $this->failLoginWithLog($request, '账号错误，如果再次输错，您的账户将被锁定。您还有'. $left_count .'次机会。', 503);
            }
            if ($login_error_count >= 5) {
                return $this->failLoginWithLog($request, '您的账号登录错误次数过多，已被锁定15分钟，请稍后再试！',504);
            }
            return $this->failLoginWithLog($request, '账号错误，请重新输入！', 501);
        }

        // 检查用户是否有新系统密码
        if ($user->password) {
            // 尝试使用新系统的密码验证
            if (!$token = Auth::attempt($credentials)) {
                $this->loginErrorLock($request);
                // 登录错误次数
                $login_error_count = Redis::get(keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_ERROR_COUNT'], $username));
                // 剩余次数
                $left_count = RedisKeyConstants::SYSTEM['MAX_ERROR_COUNT'] - $login_error_count;
                if ($login_error_count == 3) {
                    return $this->failLoginWithLog($request, '密码错误，请谨慎确认，您还有'. $left_count .'次机会。也可以尝试忘记密码功能。', 503);
                }
                if ($login_error_count == 4) {
                    return $this->failLoginWithLog($request, '密码错误，如果再次输错，您的账户将被锁定。您还有'. $left_count .'次机会。', 503);
                }
                if ($login_error_count >= 5) {
                    return $this->failLoginWithLog($request, '您的账号登录错误次数过多，已被锁定15分钟，请稍后再试！',504);
                }
                return $this->failLoginWithLog($request, '密码错误，请您重新输入！', 501);
            }
        } else {
            // 没有新系统密码，验证MD5密码
            if (md5($password) === $user->md5_password) {
                // 密码验证成功，迁移密码到新系统加密方式
                $user->password = bcrypt($password);
                // $user->md5_password = null; // 删除MD5密码
                $user->save();
                
                $token = Auth::attempt($credentials);
            } else {
                $this->loginErrorLock($request);
                // 登录错误次数
                $login_error_count = Redis::get(keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_ERROR_COUNT'], $username));
                // 剩余次数
                $left_count = RedisKeyConstants::SYSTEM['MAX_ERROR_COUNT'] - $login_error_count;
                
                if ($login_error_count == 3) {
                    return $this->failLoginWithLog($request, '密码错误，请谨慎确认，还有'. $left_count .'次机会。也可以尝试忘记密码功能。', 503);
                }
                if ($login_error_count == 4) {
                    return $this->failLoginWithLog($request, '密码错误，如果再次输错，账户将被锁定。还有'. $left_count .'次机会。', 503);
                }
                if ($login_error_count >= 5) {
                    return $this->failLoginWithLog($request, '您的账号登录错误次数过多，已被锁定15分钟，请稍后再试！',504);
                }
                return $this->failLoginWithLog($request, '密码错误，请重新输入！', 501);
            }
        }

        if (Auth::user() && $request->user()->status!==1) {
            return $this->failLoginWithLog($request, '账号已被禁用', 501);
        }

        $organization = Organization::with('model')->find(Auth::user()->organization_id);
        if ($organization && $organization->model->status!==1) {
            return $this->failLoginWithLog($request, '用户所属学校或机构已被禁用', 501);
        }

        // 检查密码强度
        $hasSpecial = preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/', $password);
        if (!$hasSpecial) {
            return $this->failLoginWithLog($request, '密码强度不足，请更新为更复杂的密码！', 502);
            // throw new HttpResponseException(response()->json([
            //     'code' => 502,
            //     'message' => '密码强度不足，请更新为更复杂的密码！',
            // ], 200));
        }

        $user = Auth::user();
        $user->role_types = $this->getUserRoleTypes();

        $province = $organization->model->province;
        $province = str_replace(['省', '市'], '', $province);
        if(!empty(ProvinceConst::$sfMapByName[$province])){
            $user->province_name = $province;
            $user->province_id = ProvinceConst::$sfMapByName[$province]['id'];
        }

        // 记录登录成功日志
        $this->logLoginSuccess($request, $user);

        return $this->respondWithToken($token, $user);
    }

    public function resetUserPasswordNoLogin(UserRequest $request)
    {
        // 接收参数
        $username = $request->input('username');
        $old_password = $request->input('old_password');
        $new_password = $request->input('new_password');
            // 查找用户
        $user = User::where('username', $username)->first();
        if (!$user) {
                // $this->throwBusinessException('用户不存在');
            return $this->failLoginWithLog($request, '账号不存在');
        }
        // 检查用户是否有旧系统的MD5密码
        if ($user->password) {
            // 如果没有MD5密码，尝试使用新系统的验证密码验证
            if (!Hash::check($old_password, $user->password)) {
                $this->throwBusinessException('旧密码错误');
            }

            // 密码验证成功，更新为新密码
            $user->password = bcrypt($new_password);
            $user->save();
        } else {
            // 验证MD5密码
            if (md5($old_password) === $user->md5_password) {
                // 密码验证成功，迁移密码到新系统加密方式
                $user->password = bcrypt($new_password);
                // $user->md5_password = null; // 删除MD5密码
                $user->save();
            } else {
                $this->throwBusinessException('旧密码错误');
            }
        }
        return $this->success("重置密码成功");

    }

    /**
     * 忘记密码 - 验证用户名和姓名是否一致
     *
     * @param string $username 用户名
     * @param string $realName 真实姓名
     * @param string $captcha 验证码
     * @param string $key 验证码key
     * @return array
     * @throws BusinessException
     */
    public function verifyUserForForgotPassword(string $username, string $realName, string $captcha, string $key): array
    {
        // 验证验证码
        if (!captcha_api_check($captcha, $key, 'math')) {
            throw new BusinessException('验证码错误或已过期', 400);
        }

        // 查找用户，只查询状态为1（启用）的用户
        $user = User::where('username', $username)
            ->where('real_name', $realName)
            ->where('status', 1)
            ->first();

        if (!$user) {
            throw new BusinessException('用户名和姓名不匹配或用户已被禁用，请检查输入信息', 501);
        }

        // 查询七天内的申请记录（添加时间+7天 > 当前时间）
        $sevenDaysAgo = now()->subDays(7);
        $existingRequest = StudentFindpass::where('user_name', $username)
            ->where('student_name', $realName)
            ->where('create_time', '>', $sevenDaysAgo) // 七天内的记录
            ->orderBy('create_time', 'desc')
            ->first();

        // 根据申请记录状态返回不同结果
        if (!$existingRequest) {
            // 状态 -1: 无申请记录，新用户
            return [
                'user_id' => $user->id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'status' => -1,
                'status_text' => '新用户',
                'message' => '用户验证成功，可以提交找回密码申请',
                'can_submit' => true
            ];
        }

        if ($existingRequest->check_status == 0) {
            // 状态 0: 待处理
            return [
                'user_id' => $user->id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'status' => 0,
                'status_text' => '待处理',
                'message' => '您已有提交记录，正在等待管理员处理，请勿重复提交',
                'can_submit' => false,
                'submit_time' => $existingRequest->create_time->format('Y-m-d H:i:s'),
                'application_id' => $existingRequest->id
            ];
        }

        if ($existingRequest->check_status == 1) {
            // 状态 1: 已通过
            return [
                'user_id' => $user->id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'status' => 1,
                'status_text' => '已通过',
                'message' => '您的申请已通过，密码已重置，可以重新提交新的申请',
                'can_submit' => true,
                'submit_time' => $existingRequest->create_time->format('Y-m-d H:i:s'),
                'process_time' => $existingRequest->check_time ? $existingRequest->check_time->format('Y-m-d H:i:s') : null,
                'application_id' => $existingRequest->id
            ];
        }

        if ($existingRequest->check_status == -1) {
            // 状态 -1: 已拒绝
            return [
                'user_id' => $user->id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'status' => -1,
                'status_text' => '已拒绝',
                'message' => '您的申请已被拒绝，可以重新提交新的申请',
                'can_submit' => true,
                'submit_time' => $existingRequest->create_time->format('Y-m-d H:i:s'),
                'process_time' => $existingRequest->check_time ? $existingRequest->check_time->format('Y-m-d H:i:s') : null,
                'application_id' => $existingRequest->id
            ];
        }

        // 其他状态（理论上不应该出现）
        return [
            'user_id' => $user->id,
            'username' => $user->username,
            'real_name' => $user->real_name,
            'status' => $existingRequest->check_status,
            'status_text' => '未知状态',
            'message' => '申请状态异常，请联系管理员',
            'can_submit' => false,
            'submit_time' => $existingRequest->create_time->format('Y-m-d H:i:s'),
            'application_id' => $existingRequest->id
        ];
    }

    /**
     * 提交找回密码申请
     *
     * @param array $requestData 请求数据
     * @return array
     * @throws BusinessException
     */
    public function submitForgotPasswordRequest(array $requestData): array
    {
        $username = $requestData['username'];
        $realName = $requestData['real_name'];
        $schoolName = $requestData['school_name'];
        $className = $requestData['class_name'];
        $gradeYear = $requestData['grade_year'];
        $newPassword = $requestData['new_password'];

        // 准备提交数据（直接使用前台输入的数据）
        $submitData = [
            'student_name' => $realName,
            'user_name' => $username,
            'school_name' => $schoolName,
            'class_name' => $className,
            'grade_year' => $gradeYear,
            'password' => bcrypt($newPassword), // 使用与现有密码相同的加密方式
            'md5_password' => md5($newPassword), // MD5密码
            'create_time' => now(),
            'check_status' => 0, // 待处理状态
        ];

        // 检查是否已存在相同的申请（防止重复提交）
        $existingRequest = StudentFindpass::where('user_name', $username)
            ->where('student_name', $realName)
            ->whereDate('create_time', today())
            ->first();

        if ($existingRequest) {
            throw new BusinessException('今日已提交过找回密码申请，请等待管理员处理', 200);
        }

        // 保存到 student_findpass 表
        $findpassRecord = StudentFindpass::create($submitData);

        if (!$findpassRecord) {
            throw new BusinessException('提交找回密码申请失败', 500);
        }

        // 发送钉钉消息，使用 Markdown 格式封装内容
        $content = "### 提交找回密码申请\n" .
        "- 学校: {$schoolName}\n" .
        "- 用户: {$realName}\n" .
        "- 内容: 账号【{$username}】找回密码申请已提交成功，请尽快处理。";
        $this->send_dingding_message($content, '提交找回密码申请', 2);

        return [
            'id' => $findpassRecord->id,
            'message' => '找回密码申请已提交成功，请等待管理员审核处理',
            'submit_time' => $findpassRecord->create_time->format('Y-m-d H:i:s')
        ];
    }

    /**
     * 查询找回密码申请进度
     *
     * @param array $queryData 查询数据
     * @return array
     * @throws BusinessException
     */
    public function queryForgotPasswordStatus(array $queryData): array
    {
        $userName = $queryData['user_name'];
        $studentName = $queryData['student_name'];

        // 查询申请记录，只需要用户名和学生姓名匹配
        $findpassRecord = StudentFindpass::where('user_name', $userName)
            ->where('student_name', $studentName)
            ->orderBy('create_time', 'desc')
            ->first();

        if (!$findpassRecord) {
            throw new BusinessException('未找到匹配的申请记录，请检查输入信息是否正确', 404);
        }

        // 根据状态返回不同的信息
        $statusInfo = $this->getStatusInfo($findpassRecord->check_status);

        return [
            'id' => $findpassRecord->id,
            'user_name' => $findpassRecord->user_name,
            'student_name' => $findpassRecord->student_name,
            'school_name' => $findpassRecord->school_name,
            'class_name' => $findpassRecord->class_name,
            'grade_year' => $findpassRecord->grade_year,
            'check_status' => $findpassRecord->check_status,
            'status_text' => $statusInfo['text'],
            'status_description' => $statusInfo['description'],
            'submit_time' => $findpassRecord->create_time->format('Y-m-d H:i:s'),
            'process_time' => $findpassRecord->check_time ? $findpassRecord->check_time->format('Y-m-d H:i:s') : null,
        ];
    }

    /**
     * 获取状态信息
     *
     * @param int $status 状态值
     * @return array
     */
    private function getStatusInfo(int $status): array
    {
        return match($status) {
            0 => [
                'text' => '待处理',
                'description' => '您的申请已提交，正在等待管理员审核处理，请耐心等待'
            ],
            1 => [
                'text' => '已处理',
                'description' => '您的申请已处理完成，密码已重置，请使用新密码登录'
            ],
            default => [
                'text' => '未知状态',
                'description' => '申请状态异常，请联系管理员'
            ]
        };
    }

    /**
     * 返回带有令牌的响应
     *
     * @param string $token JWT令牌
     * @param User $user 用户对象
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token, $user)
    {
        return $this->success([
            'token' => "bearer {$token}",
            'expires_in' => Auth::factory()->getTTL() * 60,
            'user' => $user
        ]);
    }

    /**
     * 获取当前登录用户角色类型
     * 角色类型：1学生 2教务 3老师 4教育局 5家长 999管理员
     * @return mixed
     */
    public function getUserRoleTypes()
    {
        $user = Auth::user();
        $rolesTypes = $user->roles->filter(function ($item) {
            return $item->status == 1;
        })->pluck('type')->unique()->toArray();
        unset($user->roles);

        return $rolesTypes;
    }

    // 登录账号密码错误，记录5分钟内错误次数且若超出限制锁定用户登录
    private function loginErrorLock($request): void
    {
        $username = $request->username;
        $key = keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_ERROR_COUNT'], $username);
        // 如果没有达到最大尝试次数，则增加
        if (!Redis::exists($key)) {
            Redis::setex($key, 5 * 60, 1);
        } else {
            Redis::incr($key);
        }

        // 如果达到最大尝试次数，则锁定账户
        if (Redis::get($key) >= RedisKeyConstants::SYSTEM['MAX_ERROR_COUNT']) {
            $key2 = keyBuilder(RedisKeyConstants::SYSTEM['LOGIN_LOCK_ACCOUNT'], $username);
            if (!Redis::exists($key2)) {
                Redis::setex($key2, 15 * 60, 'lock');// 锁定60分钟
            }
        }
    }

    // 获取当前用户所在机构启用状态
    public function getOrganizationStatus(Request $request)
    {
        $res = Organization::with(['model'])->find($request->user()->organization->model_id);
        return $res->model->status;
    }

    // 获取当前登录用户角色菜单
    public function getLoginUserMenus(Request $request)
    {
        // 获取当前登录用户角色集合
        $roles = $request->user()->roles->filter(function ($item) {
            return $item->status == 1;
        });
        $organization_id = $request->user()->organization_id;

        $key = keyBuilder(RedisKeyConstants::SYSTEM['ROLE_MENUS'], $roles->pluck('id')->join(','));
       if (Redis::exists($key)) {
           $cacheValue = Redis::get($key);
           return is_string($cacheValue) ? json_decode($cacheValue, true) : $cacheValue;
       }

        $result = OrganizationHasMenu::join('menus', 'menus.id', '=', 'organization_has_menus.menu_id')
            ->select('organization_has_menus.id', 'organization_has_menus.menu_id', 'organization_has_menus.parent_id',
                'organization_has_menus.date_start','organization_has_menus.date_due', 'menus.url', 'menus.icon', 'menus.sort')
            ->selectRaw("COALESCE(NULLIF(organization_has_menus.menu_alias, ''), menus.menu_name) as menu_name")
            ->where('menus.status', 1) // 基础菜单表状态正常的菜单
            ->where('organization_has_menus.status', 1) // 已购拥有菜单表状态正常的菜单
            ->where('organization_has_menus.organization_id', $organization_id)
            ->whereHas('roles', function ($query) use ($roles) {
                $query->whereIn('role_has_menus.role_id', $roles->pluck('id')); // 角色拥有的菜单
            })
            ->orderBy('organization_has_menus.sort', 'asc')
            ->get();

        $menu_list = [];
        if ($result->isNotEmpty()) {
            // 获取菜单树形结构
            $menu_list = $this->menuService->getTreeMenus($result, 1);
        }

        if (!empty($menu_list)) {
            Redis::setex($key, 60 * 60, json_encode($menu_list));
        }

        return $menu_list;
    }




    public function listBuilder(Request $request, $organization_id, $input_org=null )
    {
        $real_name = $request->input('real_name');
        $role_id = $request->input('role_id');
        $status = $request->input('status');

        return User::select('users.id', 'users.username', 'users.real_name'
                , 'users.phone', 'users.email', 'users.status', 'users.created_at', 'users.updated_at', 'users.creator', 'users.updater'
            )
            ->where('users.organization_id', $organization_id)
            ->when($real_name, fn($query) => $query->where('real_name', 'like', "%{$real_name}%"))
            ->when($role_id, fn($query) => $query->whereHas('roles', function ($query) use ($role_id) {
                $query->where('role_id', $role_id);
            }))
            ->when($input_org, fn($query) => $query->whereHas('roles', function ($query) {
                $query->where('type', SystemRoleTypeEnum::ACADEMIC_AFFAIRS->value);
            }))
            ->when($status, fn($query) => $query->where('status', $status));
    }



    /**
     * 查询某机构下的所有教务用户列表（角色类型：教务=2）
     */
    public function eduAdminListBuilder(Request $request, $organization_id)
    {
        $real_name = $request->input('real_name');
        $status = $request->input('status',1);

        return User::select(
                'users.id', 'users.username', 'users.real_name',
                'users.phone', 'users.email', 'users.status',
                'users.created_at', 'users.updated_at', 'users.creator', 'users.updater'
            )
            ->where('users.organization_id', $organization_id)
            // 仅教务角色（type = 2）
            ->whereHas('roles', function ($query) {
                $query->where('type', SystemRoleTypeEnum::ACADEMIC_AFFAIRS->value);
            })
            ->when($real_name, fn($query) => $query->where('real_name', 'like', "%{$real_name}%"))
            ->when($status, fn($query) => $query->where('status', $status));
    }

    //创建用户
    public function createUser(array $data, $password, $organizationId, $realName)
    {
        $data['organization_id'] = $organizationId;
        $data['status'] = 1;
        $data['creator'] = $realName;
        $data['updater'] = $realName;
        $data['password'] = bcrypt($password);
        $data['md5_password'] = md5($password);

        return User::forceCreate($data);
    }


    /**
     * 修改密码
     * @throws BusinessException
     */
    public function modifyPassword(ModifyPasswordRequest $request): void
    {
        try {
            $user           = $request->user();
            $oldPass        = $request->input('old_pass');
            $password       = $request->input('password');
            $passwordAgain  = $request->input('password_again');

            if ($password !== $passwordAgain) {
                $this->throwBusinessException('两次密码不一致');
            }
            if (!Hash::check($oldPass, $user->password)) {
                $this->throwBusinessException('旧密码错误');
            }

            $user->password = $password;
            $user->md5_password = md5($password);
            $user->updater = $request->user()->real_name;

            $user->save();
        } catch (\Exception $e) {
            throw new BusinessException("修改密码操作失败", 500, $e->getMessage());
        }
    }


    /**
     * 获取当前登录用户信息
     * @return array
     */
    public function getLoginUserInfo()
    {
        $user = Auth::user();
        $student = $user->student;
        if ($student) {
            // 加载学生的最新班级信息
            $student->load([
                'lastClass.grade',
                'school:id,name',
                'schoolCampus:id,campus_name'
            ]);
        }

        $rolesTypes = $user->roles->filter(function ($item) {
            return $item->status == 1;
        })->pluck('type')->unique()->toArray();
        $user->role_types = $rolesTypes;
        unset($user->roles);

        $organization = Organization::with('model')->find(Auth::user()->organization_id);
        $province = $organization->model->province;
        $province = str_replace(['省', '市'], '', $province);
        if(!empty(ProvinceConst::$sfMapByName[$province])){
            $user->province_name = $province;
            $user->province_id = ProvinceConst::$sfMapByName[$province]['id'];
        }

        return $user;
    }

    /**
     * 通过 token 获取当前用户详细信息
     * 包含角色信息、教师所带班级、学生归属班级等
     * @param Request $request
     * @return array
     * @throws BusinessException
     */
    public function getCurrentUserDetail(Request $request)
    {
        try {
            // 从 Authorization Header 中获取 token
            $token = $request->bearerToken();

            // 如果 Header 中没有，尝试从查询参数获取
            if (!$token) {
                $token = $request->query('token');
            }

            if (!$token) {
                throw new BusinessException('Token 未提供', 401);
            }

            // 使用 JWT 验证 token 并获取用户
            $user = JWTAuth::setToken($token)->user();

            if (!$user) {
                throw new BusinessException('用户不存在', 404);
            }

            // 检查用户状态是否正常
            if ($user->status != 1) {
                throw new BusinessException('用户账号已被禁用', 403);
            }

            // 获取用户基本信息
            $userInfo = [
                'id' => $user->id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'gender' => $user->gender,
                'email' => $user->email,
                'organization_id' => $user->organization_id,
                'status' => $user->status,
            ];

            // 获取角色信息
            $roles = $user->roles->filter(function ($item) {
                return $item->status == 1;
            });

            $roleInfo = [
                'role_types' => $roles->pluck('type')->unique()->toArray(),
                'role_names' => $roles->pluck('name')->toArray(),
                'roles' => $roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'type' => $role->type,
                        'type_name' => $this->getRoleTypeName($role->type)
                    ];
                })->toArray()
            ];

            $userInfo['role_info'] = $roleInfo;

            // 根据角色类型获取相应的详细信息
            $detailInfo = [];

            // 如果是学生 (type = 1)
            if (in_array(1, $roleInfo['role_types'])) {
                $detailInfo['student_info'] = $this->getStudentDetail($user);
            }

            // 如果是教师 (type = 3)
            if (in_array(3, $roleInfo['role_types'])) {
                $detailInfo['teacher_info'] = $this->getTeacherDetail($user);
            }

            // 如果是教务 (type = 2)
            if (in_array(2, $roleInfo['role_types'])) {
                $detailInfo['academic_affairs_info'] = $this->getAcademicAffairsDetail($user);
            }

            $userInfo['detail_info'] = $detailInfo;

            // 获取机构信息
            $organization = Organization::with('model')->find($user->organization_id);
            if ($organization) {
                $userInfo['organization_info'] = [
                    'id' => $organization->id,
                    'org_name' => $organization->org_name,
                    'model_type' => $organization->model_type,
                    'model_name' => $organization->model->name ?? '',
                    'province' => $organization->model->province ?? '',
                ];
            }

            return $userInfo;

        } catch (TokenExpiredException $e) {
            throw new BusinessException('Token 已过期', 401);
        } catch (TokenInvalidException $e) {
            throw new BusinessException('Token 无效', 401);
        } catch (JWTException $e) {
            throw new BusinessException('Token 解析失败', 401);
        } catch (\Exception $e) {
            throw new BusinessException('获取用户信息失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 生成用户 Token
     * @param Request $request
     * @return array
     * @throws BusinessException
     */
    public function generateUserToken(Request $request)
    {
        // 从请求中获取用户标识（可以是用户名、邮箱等）
        $credentials = $request->only(['username', 'password']);

        if (!$credentials['username']) {
            throw new BusinessException('用户名未提供', 400);
        }

        try {
            // 查找用户
            $user = User::where('username', $credentials['username'])->first();

            if (!$user) {
                throw new BusinessException('用户不存在', 404);
            }

            // 检查用户状态
            if ($user->status != 1) {
                throw new BusinessException('用户账号已被禁用', 403);
            }

            // 生成 token
            $token = JWTAuth::fromUser($user);

            $data = [
                'token' => "bearer {$token}",
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'real_name' => $user->real_name
                ]
            ];

            return $data;

        } catch (\Exception $e) {
            throw new BusinessException('Token 生成失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取当前登录用户的机构信息
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Eloquent\Model
     */
    public function getLoginUserOrgInfo(Request $request)
    {
        $organization_id = $request->user()->organization_id;
        $res = Organization::with(['model'=>function ($query) {
            $query->with('config');
        }])
            ->find($organization_id);

        return $res;
    }

    /**
     * 无登录状态获取机构及配置信息
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     */
    public function getOrganizationInfo(Request $request)
    {
        if(!jwtTokenExpired()){
            return $this->getLoginUserOrgInfo($request);
        } else{
            // 校验参数
            $validator = Validator::make($request->all(), [
                'org_name' => 'required|string',
            ]);
            // 判断路由若有org_name参数，则根据org_name获取机构及配置信息
            if (!$validator->fails()) {
                $res = Organization::with(['model'=>function ($query) {
                    $query->with('config');
                }])
                    ->where('org_name', $request->input('org_name'))
                    ->first();
                return $res;
            }

            return null;
        }
    }

    /**
     * 根据openid获取用户登录信息
     * @param string $openid
     * @return array
     * @throws BusinessException
     */
    public function getUserByOpenid($openid)
    {
        try {
            // 根据openid查找用户
            $user = User::where('openid', $openid)->first();

            if (!$user) {
                throw new BusinessException('用户不存在', 404);
            }

            // 检查用户状态
            if ($user->status != 1) {
                throw new BusinessException('用户账号已被禁用', 403);
            }

            // 获取用户角色信息
            $rolesTypes = $user->roles->filter(function ($item) {
                return $item->status == 1;
            })->pluck('type')->unique()->toArray();
            $user->role_types = $rolesTypes;
            // 清理敏感信息
            unset($user->roles);

            // 获取机构信息
            $organization = Organization::with('model')->find($user->organization_id);
            if ($organization) {
                // 检查机构状态
                if ($organization->model->status !== 1) {
                    throw new BusinessException('合作伙伴已被禁用', 403);
                }

                // 添加省份信息
                $province = $organization->model->province;
                $province = str_replace(['省', '市'], '', $province);
                if (!empty(ProvinceConst::$sfMapByName[$province])) {
                    $user->province_name = $province;
                    $user->province_id = ProvinceConst::$sfMapByName[$province]['id'];
                }

                // 添加机构信息到用户对象
                $user->organization_info = [
                    'id' => $organization->id,
                    'org_name' => $organization->org_name,
                    'model_type' => $organization->model_type,
                    'model_name' => $organization->model->name ?? '',
                    'province' => $organization->model->province ?? '',
                ];
            }

            // 生成JWT token
            $token = JWTAuth::fromUser($user);

            $data = [
                'token' => "bearer {$token}",
                'user' => $user,
                'expires_in' => config('jwt.ttl') * 60 // token过期时间（秒）
            ];

            return $data;

        } catch (\Exception $e) {
            throw new BusinessException('获取用户信息失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取用户详情
     * @param int $id
     * @return User
     * @throws BusinessException
     */
    public function getUserDetail($id)
    {
        $user = User::find($id);
        if (!$user) {
            throw new BusinessException('对象不存在', 404);
        }
        $user->role_names = $user->roles->pluck('name')->join(',');
        return $user;
    }

    /**
     * 更新用户信息
     * @param Request $request
     * @param int $id
     * @return bool
     * @throws BusinessException
     */
    public function updateUser(Request $request, $id)
    {
        $user = User::find($id);
        if (!$user) {
            throw new BusinessException('对象不存在', 404);
        }
        $data = filterRequestData('users');
        $data['updater'] = $request->user()->real_name;
        $rst = $user->fill($data)->save();
        if($rst){
            // 保存中间表 角色
            $user->roles()->sync($request->input('roles'));
            return true;
        }
        return false;
    }

    /**
     * 修改用户状态
     * @param Request $request
     * @param int $id
     * @return bool
     * @throws BusinessException
     */
    public function changeUserState(Request $request, $id)
    {
        $status = $request->input('status');
        // 校验参数
        $validator = Validator::make($request->all(), [
            'status' => 'in:1,2',
        ], [
            'status.in' => '状态值必须为1或2',
        ]);
        if ($validator->fails()) {
            throw new BusinessException($validator->errors()->first(), 400);
        }

        $user = User::find($id);
        if (!$user) {
            throw new BusinessException('对象不存在', 404);
        }
        $user->status = $status;
        $user->updater = $request->user()->real_name;
        return $user->save();
    }

    public function destroy(Request $request, $id) :bool
    {
        $loginUser = $request->user();
        $user = User::find($id);
        if (!$user) {
            throw new BusinessException('对象不存在', 404);
        }
        if($loginUser->id == $id){
            throw new BusinessException('不能删除自己', 400);
        }

        $rolesTypes = $loginUser->roles->filter(function ($item) {
            return $item->status == 1;
        })->pluck('type')->unique()->toArray();
        // 不是系统管理员，且当前用户与要删除的用户的组织不同，不能删除
        if (!in_array(SystemRoleTypeEnum::SYSTEM_ADMIN->value, $rolesTypes) && $loginUser->organization_id != $user->organization_id) {
            throw new BusinessException('不能删除其他组织的用户', 400);
        }

        // 删除用户关联的角色
        $user->roles()->detach();
        $user->delete();

        return true;
    }

    /**
     * 创建楼上远播教育第三方用户
     * @param array $userData
     * @param int $organizationId
     * @param string $realName
     * @return User
     */
    public function createYbjyThirdPartyUser(array $userData, $organizationId, $realName)
    {
        // 判断用户是否已存在
        $existingUser = User::where('openid', $userData['openid'])->first();
        if ($existingUser) {
            return $existingUser;
        }

        // 调用模型方法创建用户
        $user = $this->createUser($userData, 'Ybjy@114study', $organizationId, $realName);

        // 分配楼上远播教育第三方用户学生角色 - 角色ID为431
        $user->roles()->sync([431]);

        return $user;
    }

    /**
     * 重置用户密码
     * @param Request $request
     * @param int $id
     * @return string
     * @throws BusinessException
     */
    public function resetUserPassword(Request $request, $id)
    {
        $user = User::find($id);
        if (!$user) {
            throw new BusinessException('对象不存在', 404);
        }
        $password = $this->generatePassword();
        $user->password = bcrypt($password);
        $user->md5_password = md5($password);
        $user->updater = $request->user()->real_name;
        if($user->save()) {
            return $password;
        }
        throw new BusinessException('重置密码失败', 500);
    }

    /**
     * 生成随机密码
     * @return string
     */
    private function generatePassword()
    {
//        // 生成8位随机密码，包含大小写字母和数字
//        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
//        $password = '';
//        for ($i = 0; $i < 8; $i++) {
//            $password .= $chars[rand(0, strlen($chars) - 1)];
//        }
//        return $password;

       // 生成8位随机密码，必须包含大小写字母和数字
        do {
            $password = Str::random(8);
        } while (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $password));

        return $password;
    }

    /**
     * 获取角色类型名称
     * @param int $type
     * @return string
     */
    private function getRoleTypeName($type)
    {
        $roleTypeNames = [
            1 => '学生',
            2 => '教务',
            3 => '教师',
            4 => '教育局',
            5 => '家长',
            999 => '系统管理员'
        ];

        return $roleTypeNames[$type] ?? '未知角色';
    }

    /**
     * 获取学生详细信息
     * @param User $user
     * @return array|null
     */
    private function getStudentDetail($user)
    {
        $student = $user->student;
        if (!$student) {
            return null;
        }

        // 加载学生的最新班级信息
        $student->load([
            'lastClass.grade',
            'school:id,name',
            'schoolCampus:id,campus_name'
        ]);

        $studentInfo = [
            'id' => $student->id,
            'student_name' => $student->student_name,
            'gender' => $student->gender,
            'school_no' => $student->school_no,
            'student_no' => $student->student_no,
            'init_grade_id' => $student->init_grade_id,
            'grade_year' => $student->grade_year,
            'date_start' => $student->date_start,
            'date_due' => $student->date_due,
        ];

        // 学校信息
        if ($student->school) {
            $studentInfo['school'] = [
                'id' => $student->school->id,
                'name' => $student->school->name
            ];
        }

        // 校区信息
        if ($student->schoolCampus) {
            $studentInfo['school_campus'] = [
                'id' => $student->schoolCampus->id,
                'campus_name' => $student->schoolCampus->campus_name
            ];
        }

        // 当前班级信息
        if ($student->lastClass) {
            $studentInfo['current_class'] = [
                'id' => $student->lastClass->class_id,
                'class_name' => $student->lastClass->class_name,
                'grade_id' => $student->lastClass->grade_id,
                'school_year' => $student->lastClass->school_year,
            ];

            // 年级信息
            if ($student->lastClass->grade) {
                $studentInfo['current_grade'] = [
                    'id' => $student->lastClass->grade->id,
                    'grade_name' => $student->lastClass->grade->grade_name,
                ];
            }
        }

        return $studentInfo;
    }

    /**
     * 获取教师详细信息
     * @param User $user
     * @return array|null
     */
    private function getTeacherDetail($user)
    {
        $teacher = $user->teacher;
        if (!$teacher) {
            return null;
        }

        // 加载教师的班级、课程等信息
        $teacher->load([
            'classes.grade',
            'viewClasses.grade',
            'courses',
            'viewCourses',
            'school:id,name',
            'schoolCampus:id,campus_name'
        ]);

        $teacherInfo = [
            'id' => $teacher->id,
            'teacher_name' => $teacher->teacher_name,
        ];

        // 学校信息
        if ($teacher->school) {
            $teacherInfo['school'] = [
                'id' => $teacher->school->id,
                'name' => $teacher->school->name
            ];
        }

        // 校区信息
        if ($teacher->schoolCampus) {
            $teacherInfo['school_campus'] = [
                'id' => $teacher->schoolCampus->id,
                'campus_name' => $teacher->schoolCampus->campus_name
            ];
        }

        // 所带班级
        $teacherInfo['teaching_classes'] = $teacher->classes->map(function ($class) {
            return [
                'id' => $class->id,
                'class_name' => $class->class_name,
                'grade_id' => $class->grade_id,
                'grade_name' => $class->grade->grade_name ?? ''
            ];
        })->toArray();

        // 可查看班级
        $teacherInfo['viewable_classes'] = $teacher->viewClasses->map(function ($class) {
            return [
                'id' => $class->id,
                'class_name' => $class->class_name,
                'grade_id' => $class->grade_id,
                'grade_name' => $class->grade->grade_name ?? ''
            ];
        })->toArray();

        // 所带课程
        $teacherInfo['teaching_courses'] = $teacher->courses->map(function ($course) {
            return [
                'id' => $course->id,
                'course_name' => $course->course_name
            ];
        })->toArray();

        // 可查看课程
        $teacherInfo['viewable_courses'] = $teacher->viewCourses->map(function ($course) {
            return [
                'id' => $course->id,
                'course_name' => $course->course_name
            ];
        })->toArray();

        return $teacherInfo;
    }

    /**
     * 获取教务详细信息
     * @param User $user
     * @return array
     */
    private function getAcademicAffairsDetail($user)
    {
        // 教务人员通常有更广泛的权限，可以查看整个学校的信息
        return [
            'permissions' => [
                'can_manage_students' => true,
                'can_manage_teachers' => true,
                'can_manage_classes' => true,
                'can_view_all_data' => true
            ]
        ];
    }

    // ... existing code ...
}
