<?php

namespace App\Http\Controllers;

use App\Exceptions\BusinessException;
use App\Http\Requests\CreateThirdPartyUserRequest;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\GetUserByOpenidRequest;
use App\Http\Requests\ModifyPasswordRequest;
use App\Http\Requests\QueryForgotPasswordStatusRequest;
use App\Http\Requests\SubmitForgotPasswordRequest;
use App\Models\User;
use App\Models\Admin\Organization;

use App\Services\RoleService;
use App\Services\UserService;
use App\Services\DataSync\TeacherSyncService;
use App\Traits\CrudOperations;
use App\Traits\PasswordTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Exceptions\JWTException;
use App\Http\Requests\UserRequest;

class UserController extends Controller
{
    use PasswordTrait;
    use CrudOperations;

    protected string $model = User::class;

    protected $userService;
    protected $roleService;

    // 通过依赖注入注入 User 模型
    public function __construct(UserService $userService, RoleService $roleService)
    {
        $this->userService = $userService;
        $this->roleService = $roleService;
    }

    // ==================================== 登录用户所需接口 begin ====================================
    // 获取当前登录用户角色菜单
    public function getUserMenus(Request $request)
    {
        return $this->success($this->userService->getLoginUserMenus($request));
    }

    /**
     * 当前登录用户信息
     */
    public function getLoginUserInfo()
    {
        $user = $this->userService->getLoginUserInfo();
        return $this->success($user);
    }

    /**
     * 通过 token 获取当前用户详细信息
     * 包含角色信息、教师所带班级、学生归属班级等
     */
    public function getCurrentUserDetail(Request $request)
    {
        try {
            $userInfo = $this->userService->getCurrentUserDetail($request);
            return $this->success($userInfo, '获取用户信息成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 生成用户 Token
     */
    public function generateUserToken(Request $request)
    {
        try {
            $data = $this->userService->generateUserToken($request);
            return $this->success($data, 'Token 生成成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 当前登录用户的机构信息
     */
    public function getLoginUserOrgInfo(Request $request)
    {
        $res = $this->userService->getLoginUserOrgInfo($request);
        return $this->success($res);
    }

    /**
     * 无登录状态获取机构及配置信息
     */
    public function getOrganizationInfo(Request $request)
    {
        $res = $this->userService->getOrganizationInfo($request);
        return $this->success($res);
    }

    // 修改密码
    public function modifyPassword(ModifyPasswordRequest $request)
    {
        $this->userService->modifyPassword($request);
        return $this->message('密码修改成功');
    }

    /**
     * 用户登出
     */
    public function logOut(): JsonResponse
    {
        try {
            Auth::logout();
            return $this->message('Successfully logged out');
        } catch (JWTException $exception) {
            return $this->error('Sorry,the user cannot be logged out');
        }
    }

    /**
     * 根据openid获取用户登录信息
     */
    public function getUserByOpenid(GetUserByOpenidRequest $request)
    {
        try {
            $openid = $request->input('openid');
            $data = $this->userService->getUserByOpenid($openid);
            return $this->success($data, '获取用户信息成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    // ==================================== 登录用户所需接口 end ====================================

    // ==================================== 用户管理所需接口 begin ====================================
    // 用户列表
    public function index(Request $request)
    {
        //定义参数 是否传organization_id
        $input_org = $request->input('organization_id');
        $organization_id = $this->roleService->getOrganizationId($request);
        // 根据 organization_id 判断是否是学校，如果是则将 $input_org 设置为 true
        $org = Organization::find($organization_id);
        if ($org && $org->model_type === 'school') {
            $input_org = true;
        }
        $query = $this->userService->listBuilder($request, $organization_id, $input_org);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get()->each(function ($item) {
            $item->role_names = $item->roles->pluck('name')->join(',');
            unset($item->roles);
        });
        return $this->paginateSuccess($list, $cnt);
    }

    /**
     * 查询某机构下的所有教务用户
     */
    public function eduAdminList(Request $request)
    {
        $organization_id = $this->roleService->getOrganizationId($request);
        $query = $this->userService->eduAdminListBuilder($request, $organization_id);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get()->each(function ($item) {
            $item->role_names = $item->roles->pluck('name')->join(',');
            unset($item->roles);
        });
        return $this->paginateSuccess($list, $cnt);
    }

    public function show($id)
    {
        try {
            $user = $this->userService->getUserDetail($id);
            return $this->success($user);
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    // 创建用户
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            // 校验参数
            $validator = Validator::make($request->all(), [
                'password' => 'required|string|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
                'username' => 'required|string|unique:users,username',
                'roles' => 'required|array',
                'real_name' => 'required|string',
            ], [
                'password.regex' => '密码至少8位,必须包含大写字母、小写字母、数字',
                'password.required' => '密码至少8位,必须包含大写字母、小写字母、数字',
                'password.min' => '密码至少8位,必须包含大写字母、小写字母、数字',
                'username.unique' => '用户名已存在',
                'username.required' => '用户名不能为空',
                'roles.required' => '请选择用户角色',
                'roles.array' => '请选择用户角色',
                'real_name.required' => '请填写用户真实姓名',
                'real_name.string' => '请填写用户真实姓名',
            ]);
            if ($validator->fails()) {
                return $this->error($validator->errors()->first());
            }

            // 获取并过滤请求数据
            $data = filterRequestData("users");
            $password = $request->input('password');

            // 处理教务用户创建的特殊参数
            if ($request->has('real_name')) {
                $data['real_name'] = $request->input('real_name');
            }
            if ($request->has('gender')) {
                $data['gender'] = $request->input('gender');
            }

            $organization_id = $this->roleService->getOrganizationId($request);
            // 调用模型方法创建用户
            $user = $this->userService->createUser($data, $password, $organization_id, $request->user()->real_name);

            // 保存中间表 角色
            $user->roles()->sync($request->input('roles'));

            // 检查是否需要同步到老师接口（教务角色）
            $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
            if ($dataSyncService->isSyncEnabled()) {
                $teacherSyncService = app(TeacherSyncService::class);
                $syncResult = $teacherSyncService->syncAdminUserIfNeeded($user, $request);
            }

            DB::commit();

            return $this->message("初始密码为：{$password}");
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('用户创建失败：' . $e->getMessage());
        }
    }

    // 更新用户
    public function update(Request $request, string $id)
    {
        try {
            $result = $this->userService->updateUser($request, $id);
            if($result) {
                return $this->message("修改成功");
            } else {
                return $this->error('修改失败');
            }
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            $result = $this->userService->destroy($request, $id);
            if($result) {
                return $this->message("删除成功");
            } else {
                return $this->error('删除失败');
            }
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }


    // 修改用户状态
    public function changeState(Request $request, $id)
    {
        try {
            $result = $this->userService->changeUserState($request, $id);
            if($result) {
                return $this->message('修改成功');
            } else {
                return $this->error('修改失败');
            }
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 【远播教育】创建用户接口
     */
    public function createYbjyThirdPartyUser(CreateThirdPartyUserRequest $request)
    {
        try {
            DB::beginTransaction();
            $openid = 'yb_' . md5($request->input('phone') . '_' . $request->input('student_id'));
            if ($request->input('openid') !== $openid) {
                return $this->error('学生openid不合法');
            }

            // 准备用户数据
            $userData = [
                'username' => $request->input('openid'),
                'openid' => $request->input('openid'),
                'real_name' => $request->input('real_name'),
                'gender' => $request->input('gender'),
                'phone' => $request->input('phone'),
            ];

            $user = $this->userService->createYbjyThirdPartyUser($userData, $request->user()->organization_id, $request->user()->real_name);

            DB::commit();

            return $this->success([
                'user_id' => $user->id
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('用户创建失败');
        }
    }

    // 重置密码
    public function resetPassword(Request $request, string $id)
    {
        try {
            $password = $this->userService->resetUserPassword($request, $id);
            return $this->success($password,"重置后新密码为：{$password}");
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    public function resetPasswordNoLogin(UserRequest $request)
    {
        $validated = $request->validated();
        return $this->userService->resetUserPasswordNoLogin($request);
    }

    /**
     * 忘记密码 - 验证用户名和姓名
     * 前台用户忘记密码时，验证输入的用户名和姓名是否一致，并验证验证码
     */
    public function verifyForgotPassword(ForgotPasswordRequest $request)
    {
        try {
            $username = $request->input('username');
            $realName = $request->input('real_name');
            $captcha = $request->input('captcha');
            $key = $request->input('key');

            $result = $this->userService->verifyUserForForgotPassword($username, $realName, $captcha, $key);

            return $this->success($result, '用户验证成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 提交找回密码申请
     * 用户验证成功后，提交找回密码申请到 student_findpass 表
     */
    public function submitForgotPasswordRequest(SubmitForgotPasswordRequest $request)
    {
        try {
            $requestData = [
                'username' => $request->input('username'),
                'real_name' => $request->input('real_name'),
                'school_name' => $request->input('school_name'),
                'class_name' => $request->input('class_name'),
                'grade_year' => $request->input('grade_year'),
                'new_password' => $request->input('new_password'),
            ];

            $result = $this->userService->submitForgotPasswordRequest($requestData);

            return $this->success($result, '找回密码申请提交成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 查询找回密码申请进度
     * 前台用户查询密码申请处理状态
     */
    public function queryForgotPasswordStatus(QueryForgotPasswordStatusRequest $request)
    {
        try {
            $queryData = [
                'user_name' => $request->input('user_name'),
                'student_name' => $request->input('student_name'),
            ];

            $result = $this->userService->queryForgotPasswordStatus($queryData);

            return $this->success($result, '查询成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    // ==================================== 用户管理所需接口 end ====================================
}
