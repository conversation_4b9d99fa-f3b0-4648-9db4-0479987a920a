<?php

namespace App\Services\School\Assessment;

use App\Enums\AssessmentTypeEnum;
use App\Models\School\Assessment\Question\AssessmentCareerQuestion;
use App\Models\School\Assessment\Question\AssessmentSubjectQuestion;
use App\Models\School\Assessment\Question\AssessmentCapabilityQuestion;
use App\Models\School\Assessment\Question\AssessmentCompetencyQuestion;
use App\Models\School\Assessment\Question\AssessmentPsychologyQuestion;
use InvalidArgumentException;
use Illuminate\Support\Facades\Redis;
use App\Exceptions\BusinessException;

class QuestionService
{
    /**
     * 问题类型与模型的映射关系
     *
     * @var array
     */
    protected $questionModels = [
        AssessmentTypeEnum::CAREER => AssessmentCareerQuestion::class,
        AssessmentTypeEnum::SUBJECT => AssessmentSubjectQuestion::class,
        AssessmentTypeEnum::CAPABILITY => AssessmentCapabilityQuestion::class,
        AssessmentTypeEnum::COMPETENCY => AssessmentCompetencyQuestion::class,
        AssessmentTypeEnum::PSYCHOLOGY => AssessmentPsychologyQuestion::class,
    ];

    /**
     * 获取测评问题列表
     *
     * @param int $assessmentId
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getQuestions(int $assessmentId, $type)
    {
        try {
            // 定义缓存键
            $cacheKey = $this->getQuestionCacheKey($assessmentId, $type);
            
            // 尝试从缓存获取数据
            $cachedData = Redis::get($cacheKey);
            if ($cachedData) {
                $questions = unserialize($cachedData);
            } else {
                // 获取问题列表
                $questions = $this->fetchQuestionsByType($assessmentId, $type);
                // 将数据存入缓存（永久缓存）
                Redis::set($cacheKey, serialize($questions));
            }

            if (in_array($assessmentId, [4, 8], true)) {
                $grouped = $questions->groupBy('title');
                // 先打乱每个维度内的问题顺序
                $grouped = $grouped->map(function ($group) {
                    return $group->shuffle()->values();
                });
                // 再打乱各维度块的顺序，并拼接
                $questions = $grouped->values()->shuffle()->flatten(1)->values();
            }
            // assessmentId=10: 按 dimension_code 分组，组内随机，组间顺序随机且保持成组
            elseif ($assessmentId === 10) {
                $grouped = $questions->groupBy('dimension_code');
                // 先打乱每个维度内的问题顺序
                $grouped = $grouped->map(function ($group) {
                    return $group->shuffle()->values();
                });
                // 再打乱各维度块的顺序，并拼接
                $questions = $grouped->values()->shuffle()->flatten(1)->values();
            }
            // assessmentId=13: 前10题按 number 固定，后面随机
            elseif ($assessmentId === 13) {
                $ordered = $questions->sortBy('number')->values();
                $firstTen = $ordered->take(10);
                $rest = $ordered->slice(10)->shuffle()->values();
                $questions = $firstTen->concat($rest)->values();
            } else {
                // 其它情况：每次请求随机顺序
                $questions = $questions->shuffle()->values();
            }

            return $questions;
        } catch (\Exception $e) {
            throw new BusinessException("获取问题列表失败逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 更新问题缓存
     *
     * @param int $assessmentId
     * @param string $type
     * @return void
     */
    public function updateQuestionCache(int $assessmentId, string $type)
    {
        $this->validateQuestionType($type);
        
        // 定义缓存键
        $cacheKey = $this->getQuestionCacheKey($assessmentId, $type);
        
        // 获取最新问题列表
        $questions = $this->fetchQuestionsByType($assessmentId, $type);
        
        // 更新缓存
        Redis::set($cacheKey, serialize($questions));
    }

    /**
     * 清除问题缓存
     *
     * @param int $assessmentId
     * @param string|null $type
     * @return void
     */
    public function clearQuestionCache(int $assessmentId, ?string $type = null)
    {
        if ($type) {
            $this->validateQuestionType($type);
            
            // 清除特定类型的缓存
            $cacheKey = $this->getQuestionCacheKey($assessmentId, $type);
            Redis::del($cacheKey);
        } else {
            // 清除所有类型的缓存
            foreach (AssessmentTypeEnum::getAll() as $t) {
                $cacheKey = $this->getQuestionCacheKey($assessmentId, $t);
                Redis::del($cacheKey);
            }
        }
    }

    /**
     * 根据类型获取问题列表
     *
     * @param int $assessmentId
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function fetchQuestionsByType(int $assessmentId, string $type)
    {
        $this->validateQuestionType($type);
        
        $modelClass = $this->questionModels[$type];
        $query = $modelClass::where('assessment_id', $assessmentId);
        
        // 能力测评按 number 字段排序；其它类型不在数据库层面随机，改为在 getQuestions 中 shuffle，确保每次请求顺序不同且不受缓存固定
        if ($type === AssessmentTypeEnum::CAPABILITY) {
            $query->orderBy('number');
        }

        return $query->get();
    }

    /**
     * 验证问题类型是否有效
     *
     * @param string $type
     * @return void
     * @throws InvalidArgumentException
     */
    protected function validateQuestionType(string $type)
    {
        if (!isset($this->questionModels[$type])) {
            throw new InvalidArgumentException("不支持的问题类型: {$type}");
        }
    }

    /**
     * 获取问题缓存键
     *
     * @param int $assessmentId
     * @param string $type
     * @return string
     */
    protected function getQuestionCacheKey(int $assessmentId, string $type)
    {
        return "assessment:{$assessmentId}:questions:{$type}";
    }
}