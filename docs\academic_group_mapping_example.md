# 学职群ID映射功能说明

## 功能概述

在 `academicGroupVideos` 方法中，前端传递的学职群ID与后端数据库中的ID不一致，因此需要进行映射转换。

## 映射关系

| 前端ID | 前端名称 | 后端ID | 后端名称 |
|--------|----------|--------|----------|
| 1 | 数学与信息、经管学职群 | 4 | 数学与信息、经管群 |
| 2 | 数理与科学、技术学职群 | 3 | 数理与科学、技术群 |
| 3 | 化生与物质、健康学职群 | 1 | 化生与物质、健康群 |
| 4 | 政史与社会、行政学职群 | 8 | 政史与社会、行政群 |
| 5 | 人文与美学、设计学职群 | 2 | 人文与美学、设计群 |
| 6 | 语言与教育、传媒学职群 | 7 | 语言与教育、传媒群 |
| 7 | 艺术与体育、休闲学职群 | 6 | 艺术、体育与休闲群 |

## 使用示例

### 前端请求示例

```javascript
// 前端发送请求
fetch('/api/school/xuezhi/major/academicGroupVideos?academic_group_id=1', {
    method: 'GET',
    headers: {
        'Content-Type': 'application/json',
    }
})
.then(response => response.json())
.then(data => {
    console.log('学职群视频列表:', data);
});
```

### 后端处理流程

1. 前端传递 `academic_group_id=1`（数学与信息、经管学职群）
2. 后端接收到参数后，调用 `mapFrontendToBackendAcademicGroupId('1')`
3. 映射方法返回 `'4'`（对应后端数据库中的数学与信息、经管群）
4. 使用映射后的ID `'4'` 查询数据库：`m.AcademicGroupId like '%4%'`

### 代码实现

```php
// 在 MajorService.php 中
public function academicGroupVideosQuery(MajorRequest $request): Builder
{
    $frontendAcademicGroupId = $request->input('academic_group_id');
    
    if (!empty($frontendAcademicGroupId)) {
        // 将前端学职群ID映射为后端数据库ID
        $backendAcademicGroupId = $this->mapFrontendToBackendAcademicGroupId($frontendAcademicGroupId);
        if ($backendAcademicGroupId) {
            $query->where('m.AcademicGroupId', 'like', "%{$backendAcademicGroupId}%");
        }
    }
    
    return $query;
}

private function mapFrontendToBackendAcademicGroupId($frontendId)
{
    $mapping = [
        '1' => '4',  // 数学与信息、经管学职群 -> 数学与信息、经管群
        '2' => '3',  // 数理与科学、技术学职群 -> 数理与科学、技术群
        '3' => '1',  // 化生与物质、健康学职群 -> 化生与物质、健康群
        '4' => '8',  // 政史与社会、行政学职群 -> 政史与社会、行政群
        '5' => '2',  // 人文与美学、设计学职群 -> 人文与美学、设计群
        '6' => '7',  // 语言与教育、传媒学职群 -> 语言与教育、传媒群
        '7' => '6',  // 艺术与体育、休闲学职群 -> 艺术、体育与休闲群
    ];

    return $mapping[$frontendId] ?? null;
}
```

## 注意事项

1. 如果前端传递了不存在的学职群ID，映射方法会返回 `null`，此时不会添加过滤条件
2. 映射关系是硬编码的，如果数据库中的学职群数据发生变化，需要相应更新映射关系
3. 该功能向后兼容，如果前端不传递 `academic_group_id` 参数，则返回所有学职群的视频数据
