<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use App\Models\User;
use App\Traits\ModelChangeLogTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class Teacher extends BaseModel
{
    use SoftDeletes, ModelChangeLogTrait;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
        'creator',
        'updater',
    ];


    // 归属于学校
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }

    // 归属于校区
    public function schoolCampus()
    {
        return $this->belongsTo(SchoolCampus::class, 'school_campus_id', 'id');
    }

    // 对应一个用户
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // 获取老师所带课程
    public function courses()
    {
        return $this->belongsToMany(Course::class, 'teacher_courses', 'teacher_id', 'course_id');
    }

    // 获取老师可查看的课程
    public function viewCourses()
    {
        return $this->belongsToMany(Course::class, 'teacher_view_courses', 'teacher_id', 'course_id');
    }

    // 获取老师所带班级
    public function classes()
    {
        return $this->belongsToMany(Claass::class, 'teacher_classes', 'teacher_id', 'class_id')
            ->select('classes.id','classes.class_name','classes.grade_id','teacher_classes.school_year');
    }

    // 获取老师可查看的班级
    public function viewClasses()
    {
        return $this->belongsToMany(Claass::class, 'teacher_view_classes', 'teacher_id', 'class_id')
            ->select('classes.id','classes.class_name','classes.grade_id','teacher_view_classes.school_year');
    }


}
