<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use App\Traits\ModelChangeLogTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentClass extends BaseModel
{
    use HasFactory;
    use SoftDeletes, ModelChangeLogTrait;

    // 归属于学生
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    // 归属于班级
    public function claass()
    {
        return $this->belongsTo(Claass::class, 'class_id', 'id');
    }


}
