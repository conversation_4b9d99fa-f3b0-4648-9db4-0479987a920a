<?php

namespace App\Models\School\Assessment\Template;

use App\Models\BaseModel;
use App\Traits\ModelChangeLogTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 多元智能测评模板模型
 * 
 * 用于管理多元智能测评的模板数据
 * 
 * @property int $id 主键ID
 * @property string $intelligence_type 智能类型（可能一个或多个或多维度组合）
 * @property string $type 模板类型
 * @property int $grade_type 适用年级类别(1初中 2高中)
 * @property string $content 模板内容
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property int $update_now 更新标识
 */
class AssessmentCareerMiTemplate extends BaseModel
{
    use HasFactory;
    use SoftDeletes, ModelChangeLogTrait;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'assessment_career_mi_templates';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'intelligence_type',
        'type',
        'grade_type',
        'content',
        'update_now',
    ];

    /**
     * 应该被转换的属性
     *
     * @var array
     */
    protected $casts = [
        'grade_type' => 'integer',
        'update_now' => 'integer',
    ];

    /**
     * 根据智能类型查询
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $intelligenceType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByIntelligenceType($query, $intelligenceType)
    {
        return $query->where('intelligence_type', $intelligenceType);
    }

    /**
     * 根据模板类型查询
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 根据年级类型查询
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $gradeType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByGradeType($query, $gradeType)
    {
        return $query->where('grade_type', $gradeType);
    }
}
