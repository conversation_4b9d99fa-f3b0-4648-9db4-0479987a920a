<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Models\School\System\SchoolAssessment;
use App\Services\School\System\SchoolAssessmentService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

class SchoolAssessmentController extends Controller
{
    use CrudOperations;

    protected string $model = SchoolAssessment::class;

    protected $schoolAssessmentService;

    // 构造函数
    public function __construct(SchoolAssessmentService $schoolAssessmentService)
    {
        $this->schoolAssessmentService = $schoolAssessmentService;
    }

    //index 根据school_id查询学校测评数据
    public function index($school_id)
    {
        $schoolAssessment = $this->schoolAssessmentService->getSchoolAssessmentBySchoolId($school_id);
        return $this->success($schoolAssessment);
    }

    //获取assessments
    public function assessments(Request $request)
    {
        $assessments = $this->schoolAssessmentService->getAssessments($request);
        return $this->success($assessments);
    }

    //setSchoolAssessments
    public function setSchoolAssessments($school_id,Request $request)
    {
        $this->schoolAssessmentService->setSchoolAssessments($school_id,$request);
        return $this->message("设置成功");
    }

    //设置学校某个测评是否开放普测
    public function setSchoolAssessmentOpenPuce($school_id, Request $request)
    {
        $this->schoolAssessmentService->setSchoolAssessmentOpenPuce($school_id, $request);
        return $this->message("设置成功");
    }

    //设置学校某个测评是否开放普测报告
    public function setSchoolAssessmentOpenPuceReport($school_id, Request $request)
    {
        $this->schoolAssessmentService->setSchoolAssessmentOpenPuceReport($school_id, $request);
        return $this->message("设置成功");
    }
}
