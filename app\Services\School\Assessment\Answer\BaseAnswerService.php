<?php

namespace App\Services\School\Assessment\Answer;

use App\Enums\SystemRoleTypeEnum;
use App\Exceptions\BusinessException;
use App\Models\School\Assessment\Assessment;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Models\School\Assessment\AssessmentSchedule;
use App\Models\School\System\StudentClass;
use App\Services\BaseService;
use App\Services\School\Assessment\Score\ScoreServiceFactory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

abstract class BaseAnswerService extends BaseService implements AnswerServiceInterface
{
    /**
     * 提交答案的模板方法
     */
    public function submitAnswer(array $data, object $user): int
    {
        try {
            DB::beginTransaction();

            $context = $this->prepareContext($data, $user);

            // 执行验证步骤
            if ($this->needsAssignmentValidation($context)) {
                $context = $this->validateAssignment($context);
            }

            if ($this->needsScheduleValidation($context)) {
                $this->validateSchedule($context);
            }

            if ($this->needsDuplicateCheck($context)) {
                $this->checkDuplicateSubmission($context);
            }

            if ($this->needsDurationUpdate($context)) {
                $this->updateAssignmentDuration($context);
            }

            // 保存答案
            $this->saveAnswers($context);
            DB::commit();
            return $context['assignment_id'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException($e->getMessage(), 500, $context ?? null);
        }
    }

    /**
     * 准备上下文数据
     */
    protected function prepareContext(array $data, object $user): array
    {
        //没有assessment_task_assignment_id说明是学生自主测评或老师教务测评,要先分配一条assignment_id
        $rolesTypes = $user->roles->filter(function ($item) {
            return $item->status == 1;
        })->pluck('type')->unique()->toArray();

        if(!isset($data['assessment_task_assignment_id']) || empty($data['assessment_task_assignment_id'])){
            //1是学生
            if(in_array(SystemRoleTypeEnum::STUDENT,$rolesTypes)){
                $student = $user->student;
                if(!$student) throw new \Exception('学生不存在');
                $school_year = getCurrentSchoolYear();
                $student_class_id = StudentClass::where('school_year',$school_year)->where('student_id',$user->student->id)->value('id');
                $add_assignment = [
                    'assessment_id' => $data['assessment_id'],
                    'school_id' => $user->organization->model_id,
                    'user_id' => $user->id,
                    'duration' => $data['duration'],
                    'student_class_id' => $student_class_id,
                    'student_id' => $student->id,
                ];
            }else{//非学生测评，只要有user_id都可以做，并且不需要校验
                $add_assignment = [
                    'assessment_id' => $data['assessment_id'],
                    'school_id' => $user->organization->model_id,
                    'user_id' => $user->id,
                    'duration' => $data['duration'],
                    'student_class_id' => 0,
                    'student_id' => 0,
                ];
            }
            
            //只要不是分发就不需要校验
            $needs_check = false;

            $assignment = AssessmentTaskAssignment::create($add_assignment);
            $assignment_id = $assignment->id;
        }

        return [
            'request' => $data,
            'user_id' => $user->id,
            'school_id' => $user->organization->model_id,
            'student_id' => in_array(1,$rolesTypes) ? ($user->student ? $user->student->id : 0) : 0,
            'assessment_id' => $data['assessment_id'],
            'assignment_id' => $data['assessment_task_assignment_id'] ?? $assignment_id,
            'needs_check' => isset($needs_check) ? $needs_check : true,
        ];
    }

    /**
     * 验证任务分配
     */
    protected function validateAssignment(array $context): array
    {
        $assignment = AssessmentTaskAssignment::where('id', $context['assignment_id'])
            ->where('student_id', $context['student_id'])
            ->where('school_id', $context['school_id'])
            ->first();

        if (!$assignment) {
            throw new \Exception('无效的任务分配');
        }

        $context['assignment'] = $assignment;
        return $context;
    }

    /**
     * 验证测评时间
     */
    protected function validateSchedule(array $context): void
    {
        $schedule = AssessmentSchedule::whereHas('tasks', function($query) use ($context) {
            $query->where('id', $context['assignment']->assessment_task_id);
        })->first();

        if (!$schedule || now() < $schedule->open_time || now() > $schedule->close_time) {
            throw new \Exception('不在答题时间范围内');
        }
    }

    /**
     * 更新答题时长
     */
    protected function updateAssignmentDuration(array $context): void
    {
        $context['assignment']->update([
            'duration' => $context['request']['duration']
        ]);
    }

    /**
     * 记录错误日志
     */
    protected function logError(\Exception $e, ?array $context = null): void
    {
        Log::error('答案提交失败', [
            'error' => $e->getMessage(),
            'context' => $context,
        ]);
    }

    /**
     * 直接从答案生成报告（不保存数据）
     *
     * @param array $data 包含答案数据的数组
     * @param object $user 用户对象
     * @return array 生成的报告数据
     */
    public function generateReportFromAnswers(array $data, object $user): array
    {
        try {
            // 准备上下文数据
            $context = [
                'request' => $data,
                'user_id' => $user->id,
                'school_id' => $user->organization->model_id,
                'assessment_id' => $data['assessment_id'],
            ];

            // 获取测评信息
            $assessment = Assessment::find($data['assessment_id']);
            if (!$assessment) {
                throw new \Exception('测评不存在');
            }

            // 准备答案数据
            $answers = [];
            foreach ($data['answer'] as $answer) {
                $answers[] = [
                    'question_id' => $answer['question_id'],
                    'answer' => $answer['option'],
                ];
            }

            // 使用 ScoreServiceFactory 创建对应的分数计算服务
            $scoreService = ScoreServiceFactory::create($data['assessment_id']);

            // 计算分数
            $scoreResult = $scoreService->calculate([
                'assessment_id' => $data['assessment_id'],
                'answers' => $answers,
                'school_id' => $context['school_id'],
            ]);

            // 构建报告数据
            $reportData = [
                'assessment_id' => $data['assessment_id'],
                'assessment_name' => $assessment->name,
                'create_time' => now(),
                'school_name' => $user->organization->name,
            ];

            // 合并分数结果到报告数据
            return array_merge($reportData, $scoreResult);
        } catch (\Exception $e) {
            $this->logError($e, $data);
            throw new BusinessException($e->getMessage(), 500);
        }
    }

    /**
     * 以下是钩子方法，子类可以重写这些方法来控制验证步骤
     */
    protected function needsAssignmentValidation(array $context): bool
    {
        if(!$context['needs_check']) return false;
        return true;
    }

    protected function needsScheduleValidation(array $context): bool
    {
        if(!$context['needs_check']) return false;
        return true;
    }

    protected function needsDuplicateCheck(array $context): bool
    {
        if(!$context['needs_check']) return false;
        return true;
    }

    protected function needsDurationUpdate(array $context): bool
    {
        if(!$context['needs_check']) return false;
        return true;
    }

    /**
     * 检查是否存在重复提交
     * @param array $context 上下文数据
     * @throws \Exception 当存在重复提交时抛出异常
     */
    protected function checkDuplicateSubmission(array $context): void
    {
        $exists = $this->getAnswerModelClass()::where('assessment_task_assignment_id', $context['assignment_id'])
            ->where('school_id', $context['school_id'])
            ->exists();

        if ($exists) {
            throw new \Exception('答案已提交，不能重复提交');
        }
    }

    /**
     * 获取特定测评类型的答案模型类名
     * 例如: AssessmentCareerAnswer::class
     *
     * @return string
     */
    protected abstract function getAnswerModelClass(): string;

    protected function saveAnswers(array $context): void
    {
        # 1. 准备任务参数
        $specific_question_id_key = $this->getSpecificQuestionIdKey(); // 获取动态的问题ID键名

        $job_answer_params = [
            'assessment_id' => $context['assessment_id'],
            'answers' => collect($context['request']['answer'])->map(function ($item) use ($context, $specific_question_id_key) { // 将 $specific_question_id_key 传入闭包
                return [
                    'assessment_id' => $context['assessment_id'],
                    'assessment_task_assignment_id' => $context['assignment_id'],
                    'student_id' => $context['student_id'],
                    'school_id' => $context['school_id'],
                    $specific_question_id_key => $item['question_id'], // 使用动态获取的键名
                    'answer' => $item['option'],
                    'created_at' => now(),
                ];
            })->all(),
        ];

        $job_score_params = [
            'school_id' => $context['school_id'],
            'assessment_task_assignment_id' => $context['assignment_id'],
            'assessment_id' => $context['assessment_id'],
            'student_id' => $context['student_id'],
            'user_id' => $context['user_id'],
        ];

        # 2. 创建任务链
        $jobs = [
            new \App\Jobs\SaveAnswerJob($job_answer_params),
            new \App\Jobs\CalculateScoreJob($job_score_params),
        ];

        \Illuminate\Support\Facades\Bus::chain($jobs)->onQueue('assessment_processing')->dispatch();

        # 3. 更新测评状态，0未测评,1已测评未有结果,2有结果没有pdf_url,3有pdf_url
        AssessmentTaskAssignment::where('id', $context['assignment_id'])->update(['status' => 1]);
    }

    /**
     * 获取特定测评类型的问题ID数据库字段名
     * 例如: 'assessment_subject_question_id', 'assessment_career_question_id'
     *
     * @return string
     */
    protected abstract function getSpecificQuestionIdKey(): string; // 确保这个抽象方法已定义
}