<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\System\StudentRequest;
use App\Models\School\System\Student;
use App\Services\School\System\StudentService;
use App\Services\DataSync\DataSyncService;
use App\Services\Tool\MessageService;
use App\Services\Tool\ExcelExportService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class StudentController extends Controller
{
    use CrudOperations;

    protected string $model = Student::class;

    protected $studentService;
    protected $dataSyncService;

    // 构造函数注入
    public function __construct(StudentService $studentService, DataSyncService $dataSyncService)
    {
        $this->studentService = $studentService;
        $this->dataSyncService = $dataSyncService;
    }

    public function index(Request $request)
    {
        $query = $this->studentService->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('students.id', 'desc')->get();
        return $this->paginateSuccess($list, $cnt);
    }

    // 学生数据导出（Excel）
    public function export(Request $request)
    {
        $query = $this->studentService->listBuilder($request);
        $list = $query->orderBy('students.id', 'desc')->get();

        // 导出表头配置：键为数据键名，值为表头中文名
        $header = [
            'id' => 'ID',
            'school_name' => '学校',
            'campus_name' => '校区',
            'student_name' => '学生姓名',
            'username' => '账号',
            'gender_text' => '性别',
            'school_no' => '学籍号',
            'grade_name' => '年级',
            'class_name' => '班级',
            'school_year' => '学年',
        ];

        // 将模型行转换为导出需要的数组
        $transform = function ($row) {
            $genderVal = $row->user->gender ?? $row->gender ?? 0;
            return [
                'id' => $row->id,
                'school_name' => optional($row->school)->name,
                'campus_name' => optional($row->schoolCampus)->campus_name,
                'student_name' => $row->student_name,
                'username' => optional($row->user)->username,
                'gender_text' => $genderVal == 1 ? '男' : ($genderVal == 2 ? '女' : ''),
                'school_no' => $row->school_no,
                'grade_name' => optional(optional($row->lastClass)->grade)->grade_name,
                'class_name' => optional($row->lastClass)->class_name,
                'school_year' => optional($row->lastClass)->school_year,
            ];
        };

        $fileName = '学生信息_' . date('YmdHis') . '.xlsx';
        return Excel::download(new ExcelExportService($list, $header, '学生列表', $transform), $fileName);
    }

    public function show($id)
    {
        $record = Student::with([
            'school:id,name',
            'schoolCampus:id,campus_name',
            'lastClass.grade',
            'user.roles',
            'studentClasses' => function($query) {
                $query->with(['claass' => function($query) {
                    $query->with('grade:id,grade_name')
                          ->select('id', 'class_name', 'grade_id');
                }])->select('id', 'student_id', 'class_id', 'class_name', 'school_year', 'created_at');
            }
        ])->find($id);

        if (!$record) {
            return $this->notFound('查询对象不存在');
        }

        return $this->success($record);
    }

    // 新增学生
    public function store(StudentRequest $request)
    {
        $student = $this->studentService->store($request);

        // 调用同步接口
        try {
            $this->dataSyncService->syncSingleStudent($request);
        } catch (\Exception $e) {
            Log::warning('学生创建后同步失败', [
                'student_id' => $student->id ?? null,
                'error' => $e->getMessage()
            ]);
        }

        return $this->message('新增成功');
    }

    // 批量新增学生
    public function batchStore(StudentRequest $request)
    {
        $students = $this->studentService->batchStoreStudent($request);

        // 调用批量同步接口
        try {
            $this->dataSyncService->syncBatchStudents($request->all());
        } catch (\Exception $e) {
            Log::warning('学生批量创建后同步失败', [
                'student_count' => count($students),
                'error' => $e->getMessage()
            ]);
            
            // 内部开发，发送消息通知
            $message = [
                'title' => '学生批量创建后同步失败',
                'content' => '学生批量创建后同步失败，学生数量：' . count($students) . '，错误信息：' . $e->getMessage() ,
                'type' => 'error'
            ];
            $messageService = new MessageService();
            $messageService->sendMessage([12089545,12089546,12089547,12089548], $message);
        }

        return $this->message('批量新增学生成功');
    }

    // 学生批量升年级（自定义学生班级关系）
    public function batchUpGrade(StudentRequest $request)
    {
        $this->studentService->studentBatchUpGrade($request);
        return $this->message('批量升年级成功');
    }

    public function update(StudentRequest $request, $id)
    {
        $this->studentService->update($request, $id);
        return $this->message('更新成功');
    }

    public function destroy(Request $request, $id)
    {
        $this->studentService->destroy($request, $id);
        return $this->message('删除成功');
    }
    
    /**
     * 获取学生数据，用户学生列表穿梭框使用
     *
     * @return void
     */
    public function transferData(StudentRequest $request)
    {
        $data = $this->studentService->transferData($request);
        return $this->success($data);
    }
}
