<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Assessment\PdfZipRequest;
use App\Services\School\Assessment\BatchDownloadPdfService;
use App\Services\School\Assessment\PdfGeneratorService;
use App\Exceptions\BusinessException;
use App\Services\Tool\MessageService;
use App\Jobs\BatchDownloadPdfJob;
use App\Models\School\Assessment\AssessmentSchedule;
use App\Models\School\Assessment\Assessment;
use App\Models\School\Assessment\AssessmentBatchDownloadRecord;
use App\Jobs\BatchDownloadCompositePdfJob;

class PdfZipController extends Controller
{
    protected BatchDownloadPdfService $batchDownloadPdfService;
    protected PdfGeneratorService $pdfGeneratorService;

    public function __construct(
        BatchDownloadPdfService $batchDownloadPdfService,
        PdfGeneratorService $pdfGeneratorService
    ) {
        $this->batchDownloadPdfService = $batchDownloadPdfService;
        $this->pdfGeneratorService = $pdfGeneratorService;
    }

    /**
     * 批量下载测评报告PDF（异步处理）
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDownloadReportPdf(PdfZipRequest $request)
    {
        try {
            $params = $request->validated();
            $params['creator'] = $request->user()->real_name;
            $params['school_id'] = $request->user()->organization->model_id;
            
            // 获取记录名称
            $params['record_name'] = $this->getRecordName($params);
            
            // 创建下载记录
            $params['record_id'] = $this->createDownloadRecord($params, $params['record_name']);
            
            $params['user_id'] = $request->user()->id;
            
            // 将任务分派到队列
            BatchDownloadPdfJob::dispatch($params)->onQueue('batch_download_pdf');

            // 发送消息通知用户任务已提交
            $message = [
                'title' => 'PDF批量下载已提交',
                'content' => '测评任务：' . $params['record_name'] . ' PDF批量下载请求已提交，系统正在处理中，完成后将通知您。',
                'type' => 'success',
            ];
            $messageService = new MessageService();
            $messageService->sendMessage($request->user()->id, $message);

            return $this->success([],'PDF批量下载请求已提交，系统正在处理中，完成后将通知您。');
        } catch (\Exception $e) {
            throw new BusinessException($e->getMessage(), 500);
        }
    }
    
    /**
     * 获取记录名称
     *
     * @param array $params
     * @return string
     */
    protected function getRecordName(array $params): string
    {
        $scheduleName = AssessmentSchedule::where('id', $params['assessment_schedule_id'])
            ->value('name') ?? '';
            
        $assessmentName = Assessment::where('id', $params['assessment_id'])
            ->value('name') ?? '';
            
        return $scheduleName . $assessmentName;
    }

    /**
     * 创建下载记录
     *
     * @param array $params
     * @param string $zipName
     * @return int 记录ID
     */
    protected function createDownloadRecord(array $params, string $zipName): int
    {
        $detail = [
            'assessment_schedule_id' => $params['assessment_schedule_id'],
            'assessment_task_id' => $params['assessment_task_id'],
            'assessment_id' => $params['assessment_id'],
        ];
        
        $record = AssessmentBatchDownloadRecord::create([
            'name' => $zipName,
            'detail' => $detail,
            'creator' => $params['creator'],
            'is_completed' => false,
            'created_at' => now(),
        ]);
        
        return $record->id;
    }

    /**
     * 生成测评报告PDF
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generatePdf(PdfZipRequest $request)
    {
        $params = $request->validated();
        $assessment_id = $params['assessment_id'];
        $assessment_task_assignment_id = $params['assessment_task_assignment_id'];

        $pdf_url = $this->pdfGeneratorService->generatePdf(
            $assessment_id,
            $assessment_task_assignment_id
        );

        return $this->success($pdf_url);
    }
    
    /**
     * 生成测评报告PDF
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateCompositePdf(PdfZipRequest $request)
    {
        $params = $request->validated();
        $user_id = $params['user_id'] ?? $request->user()->id;
        $module = $params['module'];

        $pdf_url = $this->pdfGeneratorService->generateCompositePdf(
            $user_id,
            $module
        );

        return $this->success($pdf_url);
    }

    /**
     * 批量下载综合报告PDF（异步处理）
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDownloadCompositePdf(PdfZipRequest $request)
    {
        try {
            $params = $request->validated();
            $params['creator'] = $request->user()->real_name;
            $params['school_id'] = $request->user()->organization->model_id;
            
            // 创建记录名称
            $record_name = '生涯综合报告批量下载_' . date('YmdHis');
            
            // 创建下载记录
            $detail = [
                'user_ids' => $params['user_ids'],
                'module' => 'career',
            ];
            
            $record = AssessmentBatchDownloadRecord::create([
                'name' => $record_name,
                'detail' => $detail,
                'creator' => $params['creator'],
                'is_completed' => false,
                'created_at' => now(),
            ]);
            
            $params['record_id'] = $record->id;
            $params['record_name'] = $record_name;
            $params['user_id'] = $request->user()->id;
            
            // 将任务分派到队列
            BatchDownloadCompositePdfJob::dispatch($params)->onQueue('batch_download_composite_pdf');

            // 发送消息通知用户任务已提交
            $message = [
                'title' => '综合报告批量下载已提交',
                'content' => '任务：' . $record_name . ' 综合报告批量下载请求已提交，系统正在处理中，完成后将通知您。',
                'type' => 'success',
            ];
            $messageService = new MessageService();
            $messageService->sendMessage($request->user()->id, $message);

            return $this->success([],'综合报告批量下载请求已提交，系统正在处理中，完成后将通知您。');
        } catch (\Exception $e) {
            throw new BusinessException($e->getMessage(), 500);
        }
    }
}
