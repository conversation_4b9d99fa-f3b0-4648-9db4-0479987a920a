<?php

namespace App\Services\School\Assessment;

use App\Enums\SystemRoleTypeEnum;
use App\Exceptions\BusinessException;
use App\Models\School\Assessment\Assessment;
use App\Models\School\Assessment\AssessmentSchedule;
use App\Models\School\Assessment\AssessmentTask;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Models\School\Assessment\Answer\AssessmentCareerAnswer;
use App\Models\School\Assessment\Answer\AssessmentCapabilityAnswer;
use App\Models\School\Assessment\Answer\AssessmentCompetencyAnswer;
use App\Models\School\Assessment\Answer\AssessmentPsychologyAnswer;
use App\Models\School\Assessment\Answer\AssessmentSubjectAnswer;
use App\Models\School\Assessment\Question\AssessmentCareerQuestion;
use App\Models\School\Assessment\Question\AssessmentCapabilityQuestion;
use App\Models\School\Assessment\Question\AssessmentCompetencyQuestion;
use App\Models\School\Assessment\Question\AssessmentPsychologyQuestion;
use App\Models\School\Assessment\Question\AssessmentSubjectQuestion;
use App\Repositories\ScheduleRepository;
use App\Services\BaseService;
use App\Services\Tool\RedisUtilService;
use App\Services\UserService;
use Cache;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ScheduleService extends BaseService
{
    public function __construct(protected ScheduleRepository $scheduleRepository, protected UserService $userService,
        protected RedisUtilService $redisUtils)
    {
    }

    /**
     * 获取计划列表
     *
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getSchedules(Request $request)
    {
        // 验证type参数为1,2   
        $validator = $this->validateRequest($request, [
            'type' => 'required|in:1,2',
        ]);
        if ($validator->fails()) {
            $this->throwBusinessException('参数错误，type参数为1,2', 400);
        }
    
        // 获取列表类型
        $type = $request->input('type');
        $schedule_name = $request->input('schedule_name');
        $schoolId = $this->getSchoolId($request);
        // 获取当前用户的角色类型
        $role_types = $this->userService->getUserRoleTypes();
        $teacher = null;
        // 判断当前角色类型不含教务但含老师
        if (!in_array(SystemRoleTypeEnum::ACADEMIC_AFFAIRS->value, $role_types) && in_array(SystemRoleTypeEnum::TEACHER->value, $role_types)) {
                $teacher = $request->user()->teacher;
                if (!$teacher) {
                    // 如果没有找到对应的老师记录，抛出异常
                    // throw new BusinessException("找不到对应的老师记录", 404);
                    return $this->scheduleRepository->getSchedules($type, 0, null,$schedule_name);
                }
                return $this->scheduleRepository->getSchedules($type, $schoolId, $teacher,$schedule_name);
        } elseif (in_array(SystemRoleTypeEnum::ACADEMIC_AFFAIRS->value, $role_types)) {
                return $this->scheduleRepository->getSchedules($type, $schoolId, null,$schedule_name);
        }

        return $this->scheduleRepository->getSchedules($type, 0, null,$schedule_name);
    }

    /**
     * 获取计划任务的完成人数
     */
    public function getCompletedStudentCount($taskIds): int
    {
        // 检查缓存是否存在
        $cacheKey = 'assessment_completed_students_tasks:' . implode('_', $taskIds);
        $cachedCount = cache($cacheKey);
        if ($cachedCount !== null) {
            return $cachedCount;
        }

        // 只有完成了所有任务的学生才算完成了计划，直接使用HAVING子句过滤出完成了所有任务的学生数量
        $cachedCount = AssessmentTaskAssignment::query()
            ->whereIn('assessment_task_id', $taskIds)
            ->whereNotNull('standard_results')
            ->select('student_id')
            ->groupBy('student_id')
            ->havingRaw('COUNT(*) = ?', [count($taskIds)])
            ->count();

        // 缓存结果
        Cache::put($cacheKey, $cachedCount, now()->addMinutes(10)); // 缓存10分钟
        return $cachedCount;
    }

    /**
     * 创建新计划
     *
     * @param array $data
     * @return array
     */
    public function createSchedule(array $data): AssessmentSchedule
    {
        return $this->handleScheduleOperation('create', $data);
    }

    /**
     * 更新计划
     *
     * @param array $data
     * @param int $id
     * @return array
     */
    public function updateSchedule(array $data, int $id): AssessmentSchedule
    {
        return $this->handleScheduleOperation('update', $data, $id);
    }

    /**
     * 删除计划
     *
     * @param int $id
     * @return void
     * @throws \Exception
     */
    public function destroy(int $id): void
    {
        DB::beginTransaction();

        try {
            $schedule = $this->findScheduleOrFail($id);

            // 批量删除关联的assignments，避免一个个触发级联删除
            $taskIds = $schedule->tasks->pluck('id')->toArray();
            if (!empty($taskIds)) {
                AssessmentTaskAssignment::whereIn('assessment_task_id', $taskIds)->delete();
                // 批量删除任务
                AssessmentTask::whereIn('id', $taskIds)->delete();
            }

            // 最后删除计划本身
            $schedule->delete();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("测评删除计划逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 处理计划创建或更新操作
     *
     * @param string $operation 操作类型：create 或 update
     * @param array $data 请求数据
     * @param int|null $id 计划ID（更新时需要）
     * @return AssessmentSchedule
     * @throws \Exception
     *
     */
    private function handleScheduleOperation(string $operation, array $data, ?int $id = null): AssessmentSchedule
    {
        DB::beginTransaction();

        try {
            $schoolId = $this->getSchoolId();
            $schedule = $operation === 'create'
                ? $this->createScheduleRecord($data, $schoolId)
                : $this->updateScheduleRecord($data, $id, $schoolId);

            if ($operation === 'create') {
                // 创建新任务和分配
                $this->createAssignments($schedule->id, $data, $schoolId);
            } else {
                // 更新任务和分配（增量更新）
                $this->updateAssignments($schedule->id, $data, $schoolId);
            }

            DB::commit();

            return $schedule;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("处理计划创建或更新操作逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 创建计划记录
     *
     * @param array $data
     * @param int $schoolId
     * @return AssessmentSchedule
     */
    private function createScheduleRecord(array $data, int $schoolId): AssessmentSchedule
    {
        return AssessmentSchedule::create([
            'name' => $data['schedule_name'],
            'open_time' => $data['open_time'],
            'close_time' => $data['close_time'],
            'school_id' => $schoolId,
            'is_report_visible' => $data['is_report_visible'],
            'type' => $data['type'],
            'creator' => request()->user()->real_name,
            'school_year' => getCurrentSchoolYear(),
        ]);
    }

    /**
     * 更新计划记录
     *
     * @param array $data
     * @param int $id
     * @param int $schoolId
     * @return AssessmentSchedule
     * @throws \Exception
     */
    private function updateScheduleRecord(array $data, int $id, int $schoolId): AssessmentSchedule
    {
        $schedule = $this->findScheduleOrFail($id);
        $schedule->update([
            'name' => $data['schedule_name'],
            'open_time' => $data['open_time'],
            'close_time' => $data['close_time'],
            'type' => $data['type'],
            'is_report_visible' => $data['is_report_visible'],
            'creator' => request()->user()->real_name,
        ]);
        return $schedule->fresh('tasks');
    }

    /**
     * 创建任务和分配
     *
     * @param int $scheduleId
     * @param array $data
     * @param int $schoolId
     * @return void
     */
    private function createAssignments(int $scheduleId, array $data, int $schoolId): void
    {
        $assignments = [];
        foreach ($data['assessment_ids'] as $assessmentId) {
            $task = AssessmentTask::create([
                'assessment_schedule_id' => $scheduleId,
                'assessment_id' => $assessmentId,
            ]);

            $assignments = array_merge(
                $assignments,
                $this->prepareAssignments($task, $assessmentId, $data['student_info_arr'], $schoolId)
            );
        }

        if (!empty($assignments)) {
            AssessmentTaskAssignment::insert($assignments);
        }
    }

    /**
     * 更新任务和分配（增量更新）
     *
     * @param int $scheduleId
     * @param array $data
     * @param int $schoolId
     * @return void
     */
    private function updateAssignments(int $scheduleId, array $data, int $schoolId): void
    {
        // 1. 获取当前计划下的所有任务
        $existingTasks = AssessmentTask::where('assessment_schedule_id', $scheduleId)
            ->with('assignments')
            ->get();

        // 2. 获取现有的测评ID和学生ID
        $existingAssessmentIds = $existingTasks->pluck('assessment_id')->toArray();
        $newAssessmentIds = $data['assessment_ids'];

        // 3. 计算需要添加和删除的测评ID
        $assessmentIdsToAdd = array_diff($newAssessmentIds, $existingAssessmentIds);
        $assessmentIdsToRemove = array_diff($existingAssessmentIds, $newAssessmentIds);

        // 4. 删除不再需要的任务及其分配
        if (!empty($assessmentIdsToRemove)) {
            $tasksToRemove = $existingTasks->whereIn('assessment_id', $assessmentIdsToRemove);
            $taskIdsToRemove = $tasksToRemove->pluck('id')->toArray();

            if (!empty($taskIdsToRemove)) {
                // 先删除分配
                AssessmentTaskAssignment::whereIn('assessment_task_id', $taskIdsToRemove)->delete();
                // 再删除任务
                AssessmentTask::whereIn('id', $taskIdsToRemove)->delete();
            }
        }

        // 5. 添加新的任务
        if (!empty($assessmentIdsToAdd)) {
            $newAssignments = [];

            foreach ($assessmentIdsToAdd as $assessmentId) {
                $task = AssessmentTask::create([
                    'assessment_schedule_id' => $scheduleId,
                    'assessment_id' => $assessmentId,
                ]);

                $newAssignments = array_merge(
                    $newAssignments,
                    $this->prepareAssignments($task, $assessmentId, $data['student_info_arr'], $schoolId)
                );
            }

            if (!empty($newAssignments)) {
                AssessmentTaskAssignment::insert($newAssignments);
            }
        }

        // 6. 处理保留的任务，更新学生分配
        $retainedTasks = $existingTasks->whereIn('assessment_id', array_intersect($existingAssessmentIds, $newAssessmentIds));

        foreach ($retainedTasks as $task) {
            // 获取现有的学生分配
            $existingAssignments = $task->assignments;
            $existingStudentIds = $existingAssignments->pluck('student_id')->toArray();

            // 获取新的学生ID列表
            $newStudentIds = array_column($data['student_info_arr'], 'student_id');

            // 计算需要添加和删除的学生ID
            $studentIdsToAdd = array_diff($newStudentIds, $existingStudentIds);
            $studentIdsToRemove = array_diff($existingStudentIds, $newStudentIds);

            // 删除不再需要的学生分配
            if (!empty($studentIdsToRemove)) {
                AssessmentTaskAssignment::where('assessment_task_id', $task->id)
                    ->whereIn('student_id', $studentIdsToRemove)
                    ->delete();
            }

            // 添加新的学生分配
            if (!empty($studentIdsToAdd)) {
                $newStudentAssignments = [];
                $now = now();

                foreach ($data['student_info_arr'] as $studentInfo) {
                    if (in_array($studentInfo['student_id'], $studentIdsToAdd)) {
                        $newStudentAssignments[] = [
                            'assessment_task_id' => $task->id,
                            'assessment_id' => $task->assessment_id,
                            'school_id' => $schoolId,
                            'student_class_id' => $studentInfo['student_class_id'],
                            'student_id' => $studentInfo['student_id'],
                            'created_at' => $now,
                            'updated_at' => $now,
                        ];
                    }
                }

                if (!empty($newStudentAssignments)) {
                    AssessmentTaskAssignment::insert($newStudentAssignments);
                }
            }
        }
    }

    /**
     * 准备分配数据
     *
     * @param AssessmentTask $task
     * @param int $assessmentId
     * @param array $studentInfoArr
     * @param int $schoolId
     * @return array
     */
    private function prepareAssignments(AssessmentTask $task, int $assessmentId, array $studentInfoArr, int $schoolId): array
    {
        $now = now();
        return array_map(function($studentInfo) use ($task, $assessmentId, $schoolId, $now) {
            return [
                'assessment_task_id' => $task->id,
                'assessment_id' => $assessmentId,
                'school_id' => $schoolId,
                'student_class_id' => $studentInfo['student_class_id'],
                'student_id' => $studentInfo['student_id'],
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }, $studentInfoArr);
    }

    /**
     * 查找计划或抛出异常
     *
     * @param int $id
     * @return AssessmentSchedule
     * @throws \Exception
     */
    private function findScheduleOrFail(int $id): AssessmentSchedule
    {
        $schedule = AssessmentSchedule::where('id', $id)
            ->with('tasks')
            ->first();

        if (!$schedule) {
            throw new \Exception('计划不存在或无权限操作');
        }

        return $schedule;
    }

    /**
     * 获取学校ID
     *
     * @param Request|null $request
     * @return int
     */
    private function getSchoolId(?Request $request = null): int
    {
        $request ??= request();
        return $request->user()->organization->model_id;
    }

    /**
     * 获取测评计划详情
     *
     * @param int $id 计划ID
     * @return array
     */
    public function getScheduleDetail(int $id): array
    {
        $schedule = $this->findScheduleOrFail($id);

        $tasks = $this->scheduleRepository->getTasksWithAssessment($schedule->id);
        $studentAssignments = !$tasks->isEmpty()
            ? $this->scheduleRepository->getStudentAssignments($tasks->first()->id)
            : collect();

        return [
            'id' => $schedule->id,
            'schedule_name' => $schedule->name,
            'school_year' => $schedule->school_year,
            'open_time' => $schedule->open_time,
            'close_time' => $schedule->close_time,
            'is_report_visible' => $schedule->is_report_visible,
            'assessment_info_arr' => $this->formatAssessmentInfo($tasks),
            'student_info_arr' => $this->formatStudentInfo($studentAssignments)
        ];
    }

    /**
     * 格式化测评信息
     *
     * @param \Illuminate\Database\Eloquent\Collection $tasks
     * @return array
     */
    private function formatAssessmentInfo($tasks): array
    {
        $assessmentInfoArr = [];
        foreach ($tasks as $task) {
            $assessmentInfoArr[] = [
                'task_id' => $task->id,
                'assessment_id' => $task->assessment_id,
                'code' => $task->assessment->category_code,
                'assessment_name' => $task->assessment->name ?? ''
            ];
        }
        return $assessmentInfoArr;
    }

    /**
     * 格式化学生信息
     *
     * @param \Illuminate\Database\Eloquent\Collection $studentAssignments
     * @return array
     */
    private function formatStudentInfo($studentAssignments): array
    {
        $studentInfoArr = [];
        foreach ($studentAssignments as $assignment) {
            $studentInfoArr[] = [
                'assignment_id' => $assignment->id,
                'student_id' => $assignment->student_id,
                'student_name' => $assignment->student->student_name ?? '',
                'student_class_id' => $assignment->student_class_id,
                'class_name' => $assignment->studentClass->class_name ?? '',
                'standard_results' => $assignment->standard_results ?? ''
            ];
        }
        return $studentInfoArr;
    }

    /**
     * 获取计划学生名单（根据完成状态筛选）
     *
     * @param int $id 计划ID
     * @param array $filters 过滤条件
     * @return \Illuminate\Support\Collection
     */
    public function getIncompleteStudents(int $id, array $filters = [])
    {
        // 验证计划是否存在
        $schedule = $this->findScheduleOrFail($id);
        
        // 获取计划下所有任务
        $tasks = $this->scheduleRepository->getScheduleTasks($schedule->id);
        
        if ($tasks->isEmpty()) {
            return collect(); // 返回空集合
        }
        
        // 获取学生分配记录
        $assignments = $this->scheduleRepository->getTaskAssignments($tasks->pluck('id')->toArray(), $filters);
        
        // 处理学生数据（业务逻辑）
        return $this->processStudentsByCompletionStatus($assignments, $tasks, $filters);
    }
    
    /**
     * 根据完成状态处理学生数据
     *
     * @param \Illuminate\Database\Eloquent\Collection $assignments 分配记录
     * @param \Illuminate\Database\Eloquent\Collection $tasks 任务集合
     * @param array $filters 过滤条件
     * @return \Illuminate\Support\Collection
     */
    private function processStudentsByCompletionStatus($assignments, $tasks, array $filters = [])
    {
        // 获取completion_status 默认为2
        $completion_status = $filters['completion_status'] ?? 2;

        // 按学生ID分组
        $studentGroups = $assignments->groupBy('student_id');
        
        $result = collect();
        
        foreach ($studentGroups as $studentId => $studentAssignments) {
            // 获取学生信息（从第一条记录中提取）
            $firstAssignment = $studentAssignments->first();
            $student = $firstAssignment->student;
            
            // 检查学生信息是否存在
            if (!$student) continue;
            
            $studentClass = $firstAssignment->studentClass;
            
            // 检查学生班级是否存在
            if (!$studentClass) continue;
            
            $class = $studentClass->claass;
            
            // 检查班级是否存在
            if (!$class) continue;
            
            $grade = $class->grade;
            
            // 检查年级是否存在
            if (!$grade) continue;
            
            // 检查是否有未完成的任务
            $hasIncomplete = false;
            $allCompleted = true;
            $completionStatus = [];
            
            // 为每个任务检查完成状态
            foreach ($tasks as $task) {
                $taskAssignment = $studentAssignments->firstWhere('assessment_task_id', $task->id);
                $isCompleted = $taskAssignment && $taskAssignment->standard_results !== null;
                
                // 构建安全的列名
                $safeColumnName = $this->scheduleRepository->buildSafeColumnName($task);
                $completionStatus[$safeColumnName] = $isCompleted ? '已完成' : '未完成';
                
                if (!$isCompleted) {
                    $hasIncomplete = true;
                    $allCompleted = false;
                }
            }
            
            // 准备学生基本数据
            $studentData = [
                'student_no' => $student->student_no,
                'student_name' => $student->student_name,
                'gender' => $student->gender == 1 ? '男' : ($student->gender == 2 ? '女' : '未知'),
                'school_year' => $studentClass->school_year,
                'grade_name' => $grade->grade_name,
                'class_name' => $class->class_name
            ];
            
            // 合并完成状态
            $studentData = array_merge($studentData, $completionStatus);
            
            // 根据完成状态筛选
            if ($completion_status == 1) {
                // 所有名单（包含已完成和未完成的）
                $result->push($studentData); // 保持为数组格式，不转换为对象
            } elseif ($completion_status == 2 && $hasIncomplete) {
                // 未完成的学生
                $result->push($studentData); // 保持为数组格式，不转换为对象
            } elseif ($completion_status == 3 && $allCompleted) {
                // 已完成的学生
                $result->push($studentData); // 保持为数组格式，不转换为对象
            }
        }
        
        return $result;
    }

    /**
     * 获取计划统计详情
     *
     * @param Request $request
     * @return array
     * @throws BusinessException
     */
    public function getScheduleStatDetail(Request $request): array
    {
       // 获取当前用户的角色类型
       $role_types = $this->userService->getUserRoleTypes();
       $teacher = null;
       $scheduleId = $request->input('schedule_id');
       $query = AssessmentSchedule::query()
           ->select('id','name','open_time','close_time','is_report_visible','school_year','creator','created_at');

       // 判断当前角色类型不含教务但含老师
       if (!in_array(SystemRoleTypeEnum::ACADEMIC_AFFAIRS->value, $role_types) && in_array(SystemRoleTypeEnum::TEACHER->value, $role_types)) {
            $teacher = $request->user()->teacher;
            if (!$teacher) {
                throw new BusinessException('教师信息不存在');
            }

            // 添加教师可查看班级的限制
            $query->with(['tasks' => function ($query) use ($teacher) {
                $query->select('assessment_tasks.id', 'assessment_id', 'assessment_schedule_id')
                    ->join('assessments', 'assessment_tasks.assessment_id', '=', 'assessments.id')
                    ->orderBy('assessments.assessment_sort')
                    ->orderBy('assessment_tasks.id')
                    ->with(['assessment:id,name', 'assignments' => function ($query) use ($teacher) {
                        $query->whereHas('studentClass', function ($query) use ($teacher) {
                            $query->whereIn('student_classes.class_id', function ($subQuery) use ($teacher) {
                                $subQuery->select('class_id')
                                    ->from('teacher_view_classes')
                                    ->where('teacher_id', $teacher->id)
                                    ->whereRaw('teacher_view_classes.school_year = student_classes.school_year');
                            });
                        });
                    }]);
            }]);
        } else {
            $query->with(['tasks' => function ($query) {
                $query->select('assessment_tasks.id', 'assessment_id', 'assessment_schedule_id')
                    ->join('assessments', 'assessment_tasks.assessment_id', '=', 'assessments.id')
                    ->orderBy('assessments.assessment_sort')
                    ->orderBy('assessment_tasks.id')
                    ->with(['assessment:id,name', 'assignments']);
            }]);
       }

       $schedule = $query->find($scheduleId);
        if (!$schedule) {
            throw new BusinessException('计划不存在');
        }
        $totalStudents = $schedule->tasks[0]->assignments->count();
        // 计算计划下学生数量
        $schedule->total_students = $totalStudents;
        // 计算计划下已完成任务数量
        $schedule->completed_students = $this->getCompletedStudentCount($schedule->tasks->pluck('id')->toArray());
        // 完成率
        $schedule->completed_rate = $schedule->total_students > 0 ? round($schedule->completed_students / $schedule->total_students * 100) : 0;
        
        // 计算计划下每个任务的完成情况
        foreach ($schedule->tasks as $task) {
            $task->total_students = $totalStudents;
            $task->completed_students = $this->getCompletedStudentCount([$task->id]);
            $task->completed_rate = $task->total_students > 0 ? round($task->completed_students / $task->total_students * 100) : 0;
            // 计算每个任务的平均完成时间
            $task->avg_completion_time = $this->getAvgCompletionTime($task->id);
            // 计算每个任务的最大完成时间
            $task->max_completion_time = $this->getMaxCompletionTime($task->id);
            // 计算每个任务的最小完成时间
            $task->min_completion_time = $this->getMinCompletionTime($task->id);

            unset($task->assignments);
        }

        return $schedule->toArray();
    }


    /**
     * 计算任务的平均完成时间
     *
     * @param $assignments 任务分配集合
     * @return int 平均完成时间（秒）
     */
    // private function getAvgCompletionTime($assignments): int
    // {
    //     if ($assignments->isEmpty()) {
    //         return 0;
    //     }

    //     $totalDuration = 0;
    //     $validAssignments = 0;

    //     foreach ($assignments as $assignment) {
    //         if (isset($assignment->duration)) {
    //             $totalDuration += $assignment->duration;
    //             $validAssignments++;
    //         }
    //     }

    //     return $validAssignments > 0 ? round($totalDuration / $validAssignments) : 0;
    // }
    
    // 计算任务的平均完成时间
    private function getAvgCompletionTime($assessment_task_id): int
    {
        $maxCompletionTime = AssessmentTaskAssignment::where('assessment_task_id', $assessment_task_id)
        ->whereNotNull('duration')
        ->avg('duration');
        return $maxCompletionTime ?? 0;
    }

    // 计算任务的最大完成时间
    private function getMaxCompletionTime($assessment_task_id): int
    {
        $maxCompletionTime = AssessmentTaskAssignment::where('assessment_task_id', $assessment_task_id)
        ->whereNotNull('duration')
        ->max('duration');
        return $maxCompletionTime ?? 0;
    }

    // 计算任务的最小完成时间
    private function getMinCompletionTime($assessment_task_id): int
    {
        $minCompletionTime = AssessmentTaskAssignment::where('assessment_task_id', $assessment_task_id)
        ->whereNotNull('duration')
        ->min('duration');
        return $minCompletionTime ?? 0;
    }



    /**
     * 获取计划未完成学生名单
     *
     * @param int $id 计划ID
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getScheduleClasses(int $id)
    {
        $schedule = $this->findScheduleOrFail($id);
        return $this->scheduleRepository->getScheduleClasses($schedule->id);
    }

    /**
     * 构建测评问题查询
     *
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function questionBuilder(Request $request): \Illuminate\Database\Eloquent\Builder
    {
        $assessmentId = $request->input('assessment_id');
        // $assessmentTaskId = $request->input('assessment_task_id');

        // 获取测评类型
        $assessment = Assessment::findOrFail($assessmentId);
        $type = strtolower($assessment->category_code);

        // 根据测评类型获取问题和答案
        $questionQuery = $this->getQuestionsByType($type, $assessmentId);
        return $questionQuery;
    }

    /**
     * 为问题添加统计数据
     *
     * @param Request $request
     * @param \Illuminate\Database\Eloquent\Collection $questions 问题集合
     * @return \Illuminate\Database\Eloquent\Collection 带有统计信息的问题集合
     */
    public function questionStatisticsByTask(Request $request, $questions)
    {
        $assessmentId = $request->input('assessment_id');
        $assessmentTaskId = $request->input('assessment_task_id');

        // 获取测评类型
        $assessment = Assessment::findOrFail($assessmentId);
        $type = strtolower($assessment->category_code);

        // 获取答案模型类
        $answerModelMap = [
            'career' => AssessmentCareerAnswer::class,
            'capability' => AssessmentCapabilityAnswer::class,
            'competency' => AssessmentCompetencyAnswer::class,
            'psychology' => AssessmentPsychologyAnswer::class,
            'subject' => AssessmentSubjectAnswer::class,
        ];

        if (!isset($answerModelMap[$type])) {
            throw new \InvalidArgumentException("不支持的测评类型: {$type}");
        }

        $modelClass = $answerModelMap[$type];
        $questionIdColumn = "assessment_{$type}_question_id";

        // 获取同一个任务的所有学生分配ID
        $assignmentIds = AssessmentTaskAssignment::where('assessment_task_id', $assessmentTaskId)
            ->pluck('id')
            ->toArray();

        // 获取所有答案
        $answers = $modelClass::where('assessment_id', $assessmentId)
            ->whereIn('assessment_task_assignment_id', $assignmentIds)
            ->get();

        // 按问题ID分组答案
        $answersByQuestion = $answers->groupBy($questionIdColumn);

        // 计算每个问题的统计数据
        foreach ($questions as $question) {
            $questionAnswers = $answersByQuestion->get($question->id, collect());
            $totalAnswers = $questionAnswers->count();

            // 解析选项
            $options = is_string($question->options) ? json_decode($question->options, true) : $question->options;

            // 统计每个选项的答案数量
            $answerCounts = $questionAnswers->countBy('answer');

            // 构建选项统计
            $optionStats = [];
            foreach ($options as $optionKey => $optionValue) {
                $count = $answerCounts->get($optionKey, 0);
                // 百分比 = 小计 / 有效填写量
                $percentage = $totalAnswers > 0 ? round(($count / $totalAnswers) * 100) : 0;

                // 获取选项名称用于显示
                $optionName = is_array($optionValue) ? ($optionValue['name'] ?? $optionValue) : $optionValue;

                $optionStats[] = [
                    'option' => $optionKey,
                    'name' => $optionName,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }

            // 添加统计信息到问题对象
            $question->statistics = [
                'options' => $optionStats,
                'valid_count' => $totalAnswers,
                'valid_percentage' => 100, // 有效填写量的百分比始终为100%
                'average_score' => $this->calculateAverageScore($questionAnswers, $options, $totalAnswers)
            ];
        }

        return $questions;
    }

    /**
     * 计算问题的平均分
     *
     * @param \Illuminate\Support\Collection $answers 答案集合
     * @param array $options 选项数据
     * @param int $totalAnswers 总答案数
     * @return float 平均分
     */
    private function calculateAverageScore($answers, $options, $totalAnswers)
    {
        $totalScore = 0;
        $validCount = 0;

        foreach ($answers as $answer) {
            $optionKey = $answer->answer;
            if (isset($options[$optionKey])) {
                $optionValue = $options[$optionKey];
                if (is_array($optionValue) && isset($optionValue['score'])) {
                    $totalScore += $optionValue['score'];
                    $validCount++;
                }
            }
        }

        // 本题平均分 = 本题总得分 / 有效填写量
        return $totalAnswers > 0 ? round($totalScore / $totalAnswers, 1) : 0;
    }


    /**
     * 根据测评类型获取问题
     *
     * @param string $type 测评类型
     * @param int $assessmentId 测评ID
     * @return \Illuminate\Database\Eloquent\Builder 问题查询构造器
     */
    private function getQuestionsByType(string $type, int $assessmentId): \Illuminate\Database\Eloquent\Builder
    {
        $questionModelMap = [
            'career' => AssessmentCareerQuestion::class,
            'capability' => AssessmentCapabilityQuestion::class,
            'competency' => AssessmentCompetencyQuestion::class,
            'psychology' => AssessmentPsychologyQuestion::class,
            'subject' => AssessmentSubjectQuestion::class,
        ];

        if (!isset($questionModelMap[$type])) {
            throw new \InvalidArgumentException("不支持的测评类型: {$type}");
        }

        $modelClass = $questionModelMap[$type];
        return $modelClass::where('assessment_id', $assessmentId);
    }

    /**
     * 获取测评信息
     *
     * @param int $assessmentId 测评ID
     * @return Assessment 测评信息
     */
    public function getAssessmentInfo(int $assessmentId): Assessment
    {
        return Assessment::findOrFail($assessmentId);
    }
}