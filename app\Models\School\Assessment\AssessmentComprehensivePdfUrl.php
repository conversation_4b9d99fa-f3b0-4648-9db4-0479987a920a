<?php

namespace App\Models\School\Assessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class AssessmentComprehensivePdfUrl extends Model
{
    use SoftDeletes;

    protected $table = 'assessment_comprehensive_pdf_urls';

    protected $fillable = [
        'user_id',
        'module',
        'pdf_url',
    ];

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}