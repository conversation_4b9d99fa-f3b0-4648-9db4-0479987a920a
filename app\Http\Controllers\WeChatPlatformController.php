<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use EasyWeChat\OpenPlatform\Application;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Models\User;
use Tymon\JWTAuth\Facades\JWTAuth;

class WeChatPlatformController extends Controller
{

    public const APPLET_CONFIG = [
        'app_id'   => 'wxdcf8c56dd421b75f',
        'secret'   => '159f99d1ca9652d8f0018813ebdfbfdc',
        // 'redirect_uri' => 'https://apiv2.yishengya.cn/wechat/callback', // 授权回调地址
        /**
         * 接口请求相关配置，超时时间等，具体可用参数请参考：
         * https://github.com/symfony/symfony/blob/5.3/src/Symfony/Contracts/HttpClient/HttpClientInterface.php
         */
        'http' => [
            'throw' => true, // 状态码非 200、300 时是否抛出异常，默认为开启
            'timeout' => 60,
            // 'base_uri' => 'https://api.weixin.qq.com/', // 如果你在国外想要覆盖默认的 url 的时候才使用，根据不同的模块配置不同的 uri

            'retry' => true, // 使用默认重试配置
        ],
    ];

    public function params(Request $request)
    {
        // 前端 WxLogin 预取参数：mode=login|bind；绑定时可在 Header 里带 Authorization: Bearer <JWT>
        $mode = $request->input('mode', 'login');
        $token = $request->header('Authorization');

        // 生成短期 state 并缓存上下文 10 分钟
        $state = 'st_' . bin2hex(random_bytes(8));
        Redis::setex($state, 600, json_encode([
            'mode'  => $mode,
            'token' => $token,
            'ts'    => time(),
        ]));

        return $this->success([
            'appid'        => self::APPLET_CONFIG['app_id'],
            'scope'        => 'snsapi_login',
            'redirect_uri' => 'https://saas.yishengya.cn/account/weixin_login',
            'state'        => $state,
        ], 'ok');
    }

    public function login(Request $request)
    {
        // mode: login | bind
        $mode = $request->input('mode', 'login');
        // 绑定场景：优先从Header读取Authorization: Bearer <JWT>
        $token = $request->header('Authorization');

        // 生成 state：短期随机串，缓存上下文
        $state = 'st_' . bin2hex(random_bytes(8));
        // 兼容 Predis/PhpRedis 的通用写法：SET key value EX 600
        Redis::setex($state, 600, json_encode(['mode'=>$mode,'token'=>$token,'ts'=>time()]));// 10分钟有效

        // 按微信文档手动拼接二维码链接
        $appid = self::APPLET_CONFIG['app_id'];
        $redirect = urlencode('https://apiv2.yishengya.cn/wechat/callback');
        $scope = 'snsapi_login';
        $lang = $request->input('lang', 'cn');
        $qrUrl = "https://open.weixin.qq.com/connect/qrconnect?appid={$appid}&redirect_uri={$redirect}&response_type=code&scope={$scope}&state={$state}&lang={$lang}#wechat_redirect";

        return redirect($qrUrl);
    }

    public function callback(Request $request)
    {
        // 获取授权码 code
        $code = $request->input('code');
        if (!$code) {
            return $this->error('缺少code参数', 400);
        }

        try {
            $openPlatform = new Application(WeChatPlatformController::APPLET_CONFIG);
            // 直接通过 code 获取用户信息（不再需要单独获取 access_token）
            $wechatUser = $openPlatform->getOAuth()->userFromCode($code);

            Log::info('微信登录成功原始信息', $wechatUser->getRaw());
            Log::info('微信登录成功', [
                'openid' => $wechatUser->getId(),
                'getNickname' => $wechatUser->getNickname(),
                'getName' => $wechatUser->getName(),
                'getAvatar' => $wechatUser->getAvatar()
            ]);

            $openid = $wechatUser->getId();
            $nickname = $wechatUser->getNickname() ?: $wechatUser->getName();

            // 使用 state 恢复上下文（推荐），兼容bind参数（兼容旧方式）
            $mode = 'login';
            $incomingToken = null;
            $state = $request->input('state');
            if ($state && Redis::exists($state)) {
                $raw = Redis::get($state); // 取到即失效，避免重放
                Redis::del($state);
                $ctx = json_decode($raw, true) ?: [];
                $mode = $ctx['mode'] ?? 'login';
                $incomingToken = $ctx['token'] ?? null;
                if ($incomingToken && stripos($incomingToken, 'bearer ') === 0) {
                    $incomingToken = substr($incomingToken, 7);
                }
            }
            // 兼容直接传 bind=1
            if ((string)$request->input('bind') === '1') {
                $mode = 'bind';
            }

            if ($mode === 'bind') {
                // 从state、Header Authorization或查询参数token中获取JWT
                if (!$incomingToken) {
                    if ($request->has('token')) {
                        $incomingToken = $request->input('token');
                    } else {
                        $authHeader = $request->header('Authorization');
                        if ($authHeader && stripos($authHeader, 'bearer ') === 0) {
                            $incomingToken = substr($authHeader, 7);
                        }
                    }
                }

                if (!$incomingToken) {
                    return $this->error('未提供登录凭证，无法绑定。请先登录后再绑定', 401);
                }

                $currentUser = JWTAuth::setToken($incomingToken)->authenticate();
                if (!$currentUser) {
                    return $this->error('登录凭证无效或已过期，请先登录后再绑定', 401);
                }

                // 检查该openid是否已被其他账号绑定
                $exists = User::where('wx_openid', $openid)->where('id', '!=', $currentUser->id)->first();
                if ($exists) {
                    return $this->error('该微信已绑定其他账号', 409);
                }

                $currentUser->wx_openid = $openid;
                if ($nickname) {
                    $currentUser->nickname = $nickname;
                }
                $currentUser->save();

                return $this->success([
                    'message' => '微信绑定成功',
                    'user' => [
                        'id' => $currentUser->id,
                        'username' => $currentUser->username,
                        'real_name' => $currentUser->real_name ?? null,
                        'nickname' => $currentUser->nickname ?? null,
                        'wx_openid' => $currentUser->wx_openid ?? null,
                    ]
                ], '绑定成功');
            }

            // 登录模式：根据wx_openid查用户并返回token
            $user = User::where('wx_openid', $openid)->first();
            if (!$user) {
                return $this->error('该用户未绑定微信，请使用账号密码登录后绑定微信', 404);
            }

            if ($user->status != 1) {
                return $this->error('用户账号已被禁用', 403);
            }

            $jwt = JWTAuth::fromUser($user);

            return $this->success([
                'token' => "bearer {$jwt}",
                'user' => $user,
                'expires_in' => config('jwt.ttl') * 60,
            ], '登录成功');
        } catch (\Throwable $e) {
            Log::error('微信回调失败', ['error' => $e->getMessage()]);
            return $this->error('微信登录失败：' . $e->getMessage(), 500);
        }
    }

    public function unbind(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return $this->error('未登录', 401);
            }

            $user->wx_openid = null;
            $user->nickname = null;
            $user->save();

            return $this->success([
                'message' => '解绑成功',
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'real_name' => $user->real_name,
                    'nickname' => $user->nickname,
                    'wx_openid' => $user->wx_openid
                ]
            ], '解绑成功');
        } catch (\Throwable $e) {
            return $this->error('解绑失败：' . $e->getMessage(), 500);
        }
    }
}
