<?php

use App\Http\Controllers\School\System\ClassController;
use App\Http\Controllers\School\System\CourseController;
use App\Http\Controllers\School\System\GradeController;
use App\Http\Controllers\School\System\SchoolCampusController;
use App\Http\Controllers\School\System\SchoolController;
use App\Http\Controllers\School\System\StudentController;
use App\Http\Controllers\School\System\TeacherController;
use App\Http\Controllers\School\System\TeacherViewController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 学校端 系统设置 相关模块路由
Route::group(['prefix' => 'system', 'middleware' => ['auth.refresh']], function () {

    // 学校信息
    Route::group(['prefix' => 'school'], function () {
        // 学校详情
        Route::get('detail', [SchoolController::class, 'detail'])->name('system.school.detail');
        // 更新学校信息
        Route::put('update', [SchoolController::class, 'update'])->name('system.school.update');
        // 设置学校配置信息
        Route::post('set_config', [SchoolController::class, 'setConfig'])->name('system.school.set_config');
        // 获取学校下拉数据
        Route::get('options', [SchoolController::class, 'getSchoolOptions'])->name('system.school.options');

        Route::get('/campuses_grade_class', [SchoolController::class, 'classTransferData'])->name('system.student.classTransferData');
        Route::get('/class_data', [SchoolController::class, 'classTransferData'])->name('system.student.classTransferData');

    });

    // 校区管理
    Route::group(['prefix' => 'campus'], function () {
        Route::post('/all', [SchoolCampusController::class, 'all'])->name('system.campus.all');
        Route::get('/', [SchoolCampusController::class, 'index'])->name('system.campus.list');
        Route::post('/', [SchoolCampusController::class, 'store'])->name('system.campus.store');
        Route::put('/{id}', [SchoolCampusController::class, 'update'])->name('system.campus.update')->where('id', '[0-9]+');
        Route::delete('/{id}', [SchoolCampusController::class, 'destroy'])->name('system.campus.delete')->where('id', '[0-9]+');
    });

    Route::get('grades', [GradeController::class, 'index'])->name('system.grades.list');

    // 班级管理
    Route::group(['prefix' => 'class'], function () {
        Route::get('/', [ClassController::class, 'index'])->name('system.class.list');
        Route::get('/drop_classes', [ClassController::class, 'getClassList'])->name('system.class.drop_classes');
        Route::post('/', [ClassController::class, 'store'])->name('system.class.store');
        Route::post('/batch_store', [ClassController::class, 'batchStore'])->name('system.class.batch_store');
        Route::put('/{id}', [ClassController::class, 'update'])->name('system.class.update')->where('id', '[0-9]+');
        Route::delete('/{id}', [ClassController::class, 'destroy'])->name('system.class.delete')->where('id', '[0-9]+');
    });

    // 课程管理
    Route::group(['prefix' => 'course'], function () {
        Route::get('/', [CourseController::class, 'index'])->name('system.course.list');
        Route::get('/drop_courses', [CourseController::class, 'getCourseList'])->name('system.course.drop_courses');
        Route::post('/', [CourseController::class, 'store'])->name('system.course.store');
        Route::post('/batch_store', [CourseController::class, 'batchStore'])->name('system.course.batch_store');
        Route::put('/{id}', [CourseController::class, 'update'])->name('system.course.update')->where('id', '[0-9]+');
        Route::delete('/{id}', [CourseController::class, 'destroy'])->name('system.course.delete')->where('id', '[0-9]+');
    });

    // 教师管理
    Route::group(['prefix' => 'teacher'], function () {
        Route::get('/', [TeacherController::class, 'index'])->name('system.teacher.list');
        Route::get('/{id}', [TeacherController::class, 'show'])->name('system.teacher.show')->where('id', '[0-9]+');
        Route::post('/', [TeacherController::class, 'store'])->name('system.teacher.store');
        Route::post('/batch_store', [TeacherController::class, 'batchStore'])->name('system.teacher.batch_store');
        Route::get('/export', [TeacherController::class, 'export'])->name('system.teacher.export');
        Route::put('/{id}', [TeacherController::class, 'update'])->name('system.teacher.update')->where('id', '[0-9]+');
        Route::delete('/{id}', [TeacherController::class, 'destroy'])->name('system.teacher.delete')->where('id', '[0-9]+');

        Route::get('/grade_courses', [TeacherController::class, 'getGradeCourseList'])->name('system.teacher.grade_courses');
        Route::get('/grade_classes', [TeacherController::class, 'getGradeClassList'])->name('system.teacher.grade_classes');

        // 教师同步功能
        Route::post('/sync', [TeacherController::class, 'syncTeacher'])->name('system.teacher.sync');

        // 教师查看班级数据
        Route::get('/view_classes', [TeacherViewController::class, 'getViewClasses'])->name('system.teacher.view_classes');
        Route::post('/set_view_classes', [TeacherViewController::class, 'setViewClasses'])->name('system.teacher.set_view_classes');
    });

    // 学生管理
    Route::group(['prefix' => 'student'], function () {
        Route::get('/', [StudentController::class, 'index'])->name('system.student.list');
        Route::get('/{id}', [StudentController::class, 'show'])->name('system.student.show')->where('id', '[0-9]+');
        Route::post('/', [StudentController::class, 'store'])->name('system.student.store');
        Route::post('/batch_store', [StudentController::class, 'batchStore'])->name('system.student.batch_store');
        Route::post('/batch_upgrade', [StudentController::class, 'batchUpgrade'])->name('system.student.batch_upgrade');
        Route::get('/export', [StudentController::class, 'export'])->name('system.student.export');
        Route::put('/{id}', [StudentController::class, 'update'])->name('system.student.update');
        Route::delete('/{id}', [StudentController::class, 'destroy'])->name('system.student.delete');
        Route::get('/data', [StudentController::class, 'transferData'])->name('system.student.transferData');
    });
});
