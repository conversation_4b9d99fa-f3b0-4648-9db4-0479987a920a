<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoginLog extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'user_login_logs';

    /**
     * 可批量赋值的字段
     */
    protected $fillable = [
        'user_id',
        'username',
        'certificate',
        'ip_address',
        'user_agent',
        'status',
        'failure_reason',
    ];

    /**
     * 字段类型转换
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 登录状态常量
     */
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';

    /**
     * 关联用户模型
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 创建成功登录日志
     */
    public static function createSuccessLog(User $user, string $password, string $ipAddress = null, string $userAgent = null): self
    {
        return self::create([
            'user_id' => $user->id,
            'username' => $user->username,
            'certificate' => $password,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'status' => self::STATUS_SUCCESS,
        ]);
    }

    /**
     * 创建失败登录日志
     */
    public static function createFailedLog(string $username, string $reason, string $ipAddress = null, string $userAgent = null): self
    {
        return self::create([
            'user_id' => null,
            'username' => $username,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'status' => self::STATUS_FAILED,
            'failure_reason' => $reason,
        ]);
    }

    /**
     * 获取用户最近的登录记录
     */
    public static function getRecentLoginsByUser(int $userId, int $limit = 10)
    {
        return self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 获取指定时间范围内的登录统计
     */
    public static function getLoginStats(string $startDate, string $endDate)
    {
        return self::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('
                status,
                COUNT(*) as count,
                DATE(created_at) as date
            ')
            ->groupBy('status', 'date')
            ->orderBy('date', 'desc')
            ->get();
    }

    /**
     * 获取失败登录尝试（用于安全监控）
     */
    public static function getFailedAttempts(string $ipAddress = null, string $username = null, int $hours = 24)
    {
        $query = self::where('status', self::STATUS_FAILED)
            ->where('created_at', '>=', now()->subHours($hours));

        if ($ipAddress) {
            $query->where('ip_address', $ipAddress);
        }

        if ($username) {
            $query->where('username', $username);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }
}
