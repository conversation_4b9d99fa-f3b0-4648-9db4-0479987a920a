<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Traits\PaginationTrait;
use App\Services\School\Assessment\AssessmentStudentService;
use App\Services\School\Assessment\AssessmentBasicService;
use App\Models\User;

class AssessmentStudentController extends Controller
{
    use PaginationTrait;
    
    protected AssessmentStudentService $assessmentStudentService;
    protected AssessmentBasicService $assessmentBasicService;
    
    public function __construct(AssessmentStudentService $assessmentStudentService, AssessmentBasicService $assessmentBasicService)
    {
        $this->assessmentStudentService = $assessmentStudentService;
        $this->assessmentBasicService = $assessmentBasicService;
    }

    /**
     * 获取学生的测评计划列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function mySchedules(Request $request): JsonResponse
    {
        $student = $request->user()->student;
        $school_id = $request->user()->organization->model_id;
        $type = $request->input('type',1);

        if (!$student) {
            return $this->error('未找到学生信息');
        }
        
        $query = $this->assessmentStudentService->getStudentSchedules($student->id,$school_id,$type);
        $total = $query->count();

        //前端没做分页，分页参数设为默认100
        $request->merge(['page_size' => 100]);
        $list = $this->scopePagination($query)->get();
        
        return $this->paginateSuccess($list, $total);
    }

    /**
     * 获取学生的测评计划名称
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function scheduleName(int $id): JsonResponse
    {
        $schedule_name = $this->assessmentStudentService->getScheduleName($id);

        return $this->success($schedule_name);
    }
    
    /**
     * 获取学生的测评任务列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function myTasks(Request $request, int $scheduleId): JsonResponse
    {
        $studentId = $request->user()->student->id;
        $school_id = $request->user()->organization->model_id;
        $query = $this->assessmentStudentService->getStudentTasks($studentId,$school_id,$scheduleId);
        $total = $query->count();

        //前端没做分页，分页参数设为默认100
        $request->merge(['page_size' => 100]);
        $list = $this->scopePagination($query)->get();

        //计划已结束，且任务未完成，任务状态改为-1
        $now_time = date('Y-m-d H:i:s');
        $close_time = $this->assessmentStudentService->getScheduleCloseTime($scheduleId,'close_time');
        foreach ($list as $task) {
            if($close_time < $now_time && $task->status == 0){
                $task->status = -1;
            }
        }

        return $this->paginateSuccess($list, $total);
    }
    
    /**
     * 测评首页展示信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(int $id): JsonResponse
    {
        $data = $this->assessmentStudentService->getAssessmentInfo($id);
        return $this->success($data);
    }
    
    /**
     * 获取自主探索测评分类列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function assessmentList(Request $request)
    {
        $school_id = $request->user()->organization->model_id;
        $user_id = $request->user()->id;
        $type = $request->input('type');
        //'student'是学生端指定给学生自主探索开放的测评
        $result = $this->assessmentBasicService->getAssessmentList($school_id, $type, 'student', $user_id, null);
        
        return $this->success($result);
    }

    /**
     * 个人综合报告
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function compositeReport(Request $request)
    {
        $module = $request->input('module','career');
        
        $user_id = $request->input('user_id');

        if($user_id){
            $school_id = User::where('users.id', $user_id)
            ->join('organizations', 'organizations.id', '=', 'users.organization_id')
            ->value('organizations.model_id');
        }else{
            $user_id = $request->user()->id;
            $school_id = $request->user()->organization->model_id;
        }

        $report = $this->assessmentStudentService->compositeReport($module,$user_id,$school_id);
        
        return $this->success($report);
    }

    /**
     * 我的画像生涯词云
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function keyword(Request $request)
    {
        $user_id = $request->input('user_id') ?? $request->user()->id;
        $school_id = $request->user()->organization->model_id;

        $report = $this->assessmentStudentService->keyword($user_id,$school_id);

        return $this->success($report);
    }

    /**
     * 生涯画像-生涯数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function tag(Request $request)
    {
        $user_id = $request->input('user_id') ?? $request->user()->id;
        $school_id = $request->user()->organization->model_id;

        $report = $this->assessmentStudentService->tag($user_id,$school_id);
        return $this->success($report);
    }

    /**
     * 生涯画像-生涯数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function assessmentData(Request $request)
    {
        $assessment_id = $request->input('assessment_id');
        $user_id = $request->input('user_id') ?? $request->user()->id;
        $school_id = $request->user()->organization->model_id;

        $report = $this->assessmentStudentService->assessmentData($assessment_id,$user_id,$school_id);
        return $this->success($report);
    }

    // 获取用户的所有最新已做测评报告
    public function getUserReports(Request $request){
        $user_id = $request->user()->id;
        $school_id = $request->user()->organization->model_id;
        $data = $this->assessmentStudentService->getUserReports($user_id, $school_id);
        return $this->success($data);
    }
}
