<?php

namespace App\Console\Commands;

use App\Models\School\System\School;
use App\Models\School\System\SchoolCampus;
use App\Services\School\System\StudentService;
use Illuminate\Console\Command;

/**
 * 数据同步命令
 * 用于批量同步历史数据或手动触发同步
 */
class OnekeyUpStudentGradeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onekeyUpStudentGrade';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '数据同步命令 - 用于批量同步历史数据或手动触发同步';

    protected $studentService;

    /**
     * Create a new command instance.
     */
    public function __construct(StudentService $studentService)
    {
        parent::__construct();
        $this->studentService = $studentService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 获取未过期且启用自动升级的学校
        $schools = School::where('status', 1)
            ->where('date_due', '>=', date('Y-m-d'))
            ->where('auto_up_student_grade', 1) // 启用自动升级
            ->orderBy('id', 'asc')
            ->get();

        foreach ($schools as $school) {
            // 获取校区
            $school_campuses = SchoolCampus::where('school_id', $school->id);
            foreach ($school_campuses as $school_campus){
                $this->studentService->batchStudentUpGradeAll($school->id, $school_campus->id);
            }
        }
    }


}
