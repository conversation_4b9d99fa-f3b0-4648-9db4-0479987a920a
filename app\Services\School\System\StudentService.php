<?php

namespace App\Services\School\System;

use App\Constants\SchoolConstants;
use App\Exceptions\BusinessException;
use App\Http\Requests\School\System\StudentRequest;
use App\Models\Role;
use App\Models\School\System\Claass;
use App\Models\School\System\School;
use App\Models\School\System\Grade;
use App\Models\School\System\SchoolCampus;
use App\Models\School\System\Student;
use App\Models\School\System\StudentClass;
use App\Models\User;
use App\Services\BaseService;
use App\Services\Tool\MessageService;
use App\Services\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StudentService extends BaseService
{
    protected ClassService $classService;
    protected UserService $userService;

    public function __construct(ClassService $classService, UserService $userService)
    {
        $this->classService = $classService;
        $this->userService = $userService;
    }

    public function listBuilder(Request $request)
    {
        // 获取当前登录用户所属学校id
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request->input('school_campus_id');
        $grade_id = $request->input('grade_id');
        $class_id = $request->input('class_id');
        $student_name = $request->input('student_name');
        $school_year = $request->input('school_year');

        // 检查学校是否在上海，以便使用正确的年级映射
        $school = School::find($school_id);
        $isShanghai = SchoolConstants::isShanghai($school->province);

        // 构建基础查询
        $query = Student::where('students.school_id', $school_id)
        ->when($school_campus_id, fn($query) => $query->where('students.school_campus_id', $school_campus_id))
        ->when($student_name, fn($query) => $query->where('students.student_name', 'like', "%$student_name%"));

       // 应用年级筛选 - 使用子查询获取最新的学生班级关系
       if ($grade_id || $class_id || $school_year) {
           $query->whereHas('lastClass', function ($q) use ($grade_id, $class_id, $school_year) {
               // 仅匹配“最新”的学生班级关系（按 student_classes.id 最大的一条）
               $q->whereRaw("student_classes.id = (select max(sc2.id) from student_classes sc2 where sc2.student_id = students.id and sc2.deleted_at is null)");

               if ($grade_id) {
                   $q->where('classes.grade_id', $grade_id);
               }
               if ($class_id) {
                   $q->where('classes.id', $class_id);
               }
               if ($school_year) {
                   $q->where('student_classes.school_year', $school_year);
               }
           });
       }

        // 预加载关联数据
        $query->with([
            'school:id,name', 'schoolCampus:id,campus_name',  'user:id,username,gender',
            // 'lastClass.grade' 
            'lastClass' => function($query) use ($isShanghai) {
                // 已经在模型中定义了排序和限制，这里只需要加载年级关系
                $query->with(['grade' => function($query) use ($isShanghai) {
                    if ($isShanghai) {
                        $query->select('id', 'alisa_name as grade_name');
                    } else {
                        $query->select('id', 'grade_name');
                    }
                }]);
            }
        ]);

        return $query;
    }

    public function listBuilderOld(Request $request)
    {
        // 获取当前登录用户所属学校id
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request->input('school_campus_id');
        $grade_id = $request->input('grade_id');
        $student_name = $request->input('student_name');

        $query = Student::where('school_id', $school_id)
            ->when($school_campus_id, fn($query) => $query->where('school_campus_id', $school_campus_id))
            ->when($student_name, fn($query) => $query->where('student_name', 'like', "%$student_name%"));
        // 应用年级筛选 - 使用子查询获取最新的学生班级关系
        if ($grade_id) {
            $query->leftJoin('classes','student_classes.class_id', '=','classes.id')
                ->where('classes.grade_id', $grade_id)
                ->whereNull('student_classes.deleted_at')
                ->orderBy('student_classes.id', 'desc')
                ->limit(1);

        }
        $query->with('school:id,name', 'schoolCampus:id,campus_name',  'user:id,username,gender', 'lastClass.grade');

        return $query;
    }
    
    public function store(StudentRequest $request)
    {
        $class = Claass::find($request['class_id']);
        if (!$class) {
            $this->throwBusinessException('班级不存在');
        }

        // 获取organization_id
        $organization_id = $request->input('organization_id');
        // 如果请求参数中没有organization_id，则取当前登录用户对应机构的的school_id
        if (!$organization_id) {
            $organization_id = $request->user()->organization_id;
        }
        $school_id = $this->classService->getSchoolId($request);
        // 校区
        $school_campus_id = $request['school_campus_id'];
        if ($school_campus_id != $class->school_campus_id) {
            $this->throwBusinessException('当前班级与校区不匹配');
        }

        // 查找学号是否已存在
        $existingStudent = Student::where('school_id', $school_id)
            ->where('school_no', $request['school_no'])
            ->first();
        if ($existingStudent) {
            $this->throwBusinessException('学号已存在，请核实是否重复新增当前学号学生');
        }

        try {
            DB::beginTransaction();

            $real_name = $request->user()->real_name;
            $user = User::create([
                'organization_id' => $organization_id,
                'real_name' => $request['student_name'],
                'username' => $request['username'],
                'password' => bcrypt($request['password']),
                'gender' => $request['gender'],
                'status' => 1,
                'creator' => $real_name
            ]);

            // 保存用户所属角色
            $user->roles()->sync($request['roles']);

            $student = Student::create([
                'user_id' => $user->id,
                'school_id' => $school_id,
                'school_campus_id' => $school_campus_id,
                'student_name' => $request['student_name'],
                'gender' => $request['gender'],
                'school_no' => $request['school_no'],
                'student_no' => $request['school_no'],
                'init_grade_id' => $request['init_grade_id'],
                'grade_year' => $request['grade_year'],
//                'date_start' => $request['date_start'],
//                'date_due' => $request['date_due'],
                'creator' => $real_name
            ]);

            // 创建学生所属班级关系
            $this->createStudentClass($student->id, $class->id, $class->class_name, $request['school_year'], $real_name, '新增学生自动创建');

            DB::commit();

            return $student;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("新增学生失败", 500, $e->getMessage());
        }
    }

    // 批量新增学生
    public function batchStoreStudent(StudentRequest $request)
    {
        // 获取organization_id
        $organization_id = $request->input('organization_id');
        // 如果请求参数中没有organization_id，则取当前登录用户对应机构的的school_id
        if (!$organization_id) {
            $organization_id = $request->user()->organization_id;
        }
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $grade_year = $request['grade_year'];
//        $date_start = $request['date_start'];
//        $date_due = $request['date_due'];
        $student_list = $request['students'];

        // 取出学生集合的所有学籍号值
        $school_no_list = array_column($student_list, 'school_no');
        // 判断学籍号是否已存在
        $student_school_no_list = Student::where('school_id', $school_id)->whereIn('school_no', $school_no_list)->get();
        if ($student_school_no_list->count()>0) {
            $this->throwBusinessException('导入数据中学生学籍号在数据库中已存在，请核实是否重复新增，学号列表为：' . $student_school_no_list->implode('school_no', ','));
        }

        // 取出学生集合的所有角色
        $role_name_list = array_unique(array_column($student_list, 'role_name'));
        $role_list = Role::where('organization_id', $organization_id)
            ->where('status', 1)
            ->where('type', 1) // 角色类型为1，学生角色
            ->whereIn('name', $role_name_list)
            ->get();
        if ($role_list->count() != count($role_name_list)) {
            $role_diff_list = array_diff($role_name_list, $role_list->pluck('name')->toArray());
            $this->throwBusinessException('导入数据中学生类角色名称在数据库中不存在：'. implode(',', $role_diff_list));
        }

        // 取出学生集合的所有班级名称
        $class_name_list = array_unique(array_column($student_list, 'class_name'));
        $class_list = Claass::where('school_id', $school_id)
            ->where('school_campus_id', $school_campus_id)
            ->where('grade_id', $grade_id)
            ->whereIn('class_name', $class_name_list)->get();
        if ($class_list->count() != count($class_name_list)) {
            $class_diff_list = array_diff($class_name_list, $class_list->pluck('class_name')->toArray());
            $this->throwBusinessException('导入数据中有班级名称在数据库中不存在：'. implode(',', $class_diff_list));
        }

        try {
            DB::beginTransaction();

            // 开始批量创建学生
            $real_name = $request->user()->real_name;

            $student_result_list = [];
            foreach ($student_list as $student) {
                $user = User::create([
                    'organization_id' => $organization_id,
                    'real_name' => $student['student_name'],
                    'username' => $student['username'],
                    'password' => bcrypt($student['password']),
                    'gender' => $student['gender'] ? ($student['gender'] == '男' ? 1 : 2) : 1,
                    'status' => 1,
                    'creator' => $real_name
                ]);

                // 根据角色名称获取角色id
                $role = Role::where('organization_id', $organization_id)
                    ->where('status', 1)
                    ->where('type', 1) // 角色类型为1，学生角色
                    ->where('name', $student['role_name'])
                    ->first();
                // 保存用户所属角色
                $user->roles()->sync($role->id);

                $studentResult = Student::create([
                    'user_id' => $user->id,
                    'school_id' => $school_id,
                    'school_campus_id' => $school_campus_id,
                    'student_name' => $student['student_name'],
                    'gender' => $student['gender'] == '男' ? 1 : 2,
                    'school_no' => $student['school_no'],
                    'student_no' => $student['school_no'],
                    'init_grade_id' => $grade_id,
                    'grade_year' => $grade_year,
//                    'date_start' => $date_start,
//                    'date_due' => $date_due,
                    'creator' => $real_name
                ]);
                $student_result_list[] = $studentResult;

                $class = Claass::where('school_id', $school_id)
                    ->where('school_campus_id', $school_campus_id)
                    ->where('grade_id', $grade_id)
                    ->where('class_name', $student['class_name'])
                    ->first();
                // 创建学生所属班级关系
                $this->createStudentClass($studentResult->id, $class->id, $class->class_name, $student['school_year'], $real_name, '批量新增学生自动创建');
            }

            DB::commit();

            // 获取学校名称
            $school = School::find($school_id);
            $school_name = $school->name;
            // 内部开发，发送消息通知
            $message = [
                'title' => '学生批量新增成功',
                'content' => '学生批量新增信息：学校：'. $school_name . '(id：'. $school_id . '），年级ID：'. $grade_id . '年级年份：'. $grade_year . '，学生数量：' . count($student_result_list),
                'type' => 'info'
            ];
            $messageService = new MessageService();
            $messageService->sendMessage([$request->user()->id], $message);

            return $student_result_list;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("批量新增学生失败", 500, $e->getMessage());
        }
    }

    // 学生批量升年级（自定义学生班级关系）
    public function studentBatchUpGrade(StudentRequest $request)
    {
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $school_year = $request['school_year'];
        $student_list = $request['students'];

        // 取出学生集合的目标班级所有班级名称
        $class_name_list = array_unique(array_column($student_list, 'class_name'));
        $class_list = Claass::where('school_id', $school_id)
            ->where('school_campus_id', $school_campus_id)
            ->where('grade_id', $grade_id)
            ->whereIn('class_name', $class_name_list)
            ->get(['id','class_name']);
        if ($class_list->count() != count($class_name_list)) {
            $class_diff_list = array_diff($class_name_list, $class_list->pluck('class_name')->toArray());
            $this->throwBusinessException('导入数据中有班级名称在数据库中不存在'. '（校区ID：'. $school_campus_id . '，年级ID：'. $grade_id . '）：'. implode(',', $class_diff_list));
        }

        $studentIds = array_column($student_list, 'student_id');
        $students = Student::whereIn('id', $studentIds)
            ->where('school_id', $school_id)
            ->where('school_campus_id', $school_campus_id)
            ->get();
        // 判断student_id是否有重复的
        if (count($students) != count($studentIds)) {
            $diff_list = array_diff($studentIds, $students->pluck('id')->toArray());
            throw new BusinessException('批量升级数据中有学生ID重复或不属于当前学校校区的学生，学生ID为：'. implode(',', $diff_list));
        }

        // 判断批量升级的数据，数据库中是否已存在
        $existing = StudentClass::whereIn('student_id', $studentIds)
            ->where('school_year', $school_year)
            ->pluck('student_id')
            ->all();
        if (!empty($existing)) {
            $existingStudents = Student::whereIn('id', $existing)
                ->pluck('student_name')
                ->all();
            $existingStudents = implode(',', array_values($existingStudents));
            throw new BusinessException("批量升级学生年级失败：数据库中已存在该学生当前学年数据，请勿重复升级。学校ID为：{$school_id}，校区ID为：{$school_campus_id}，已存在的学生：{$existingStudents}", 500);
        }

        try {
            DB::beginTransaction();

            $now = now();
            $real_name = $request->user()->real_name;
            // 班级名称 -> 班级对象
            $classByName = $class_list->keyBy('class_name');
            foreach ($student_list as $item) {
                $class = $classByName->get($item['class_name']);

                // 创建学生班级关系，调用此方法，才会记录变更日志，批处理insert不走模型，故无法创建日志
                $this->createStudentClass($item['student_id'], $class->id, $class->class_name, $school_year, $real_name, '批量升级学生年级班级' . $now);
            }

            DB::commit();

            // 获取学校名称
            $school = School::find($school_id);
            $school_name = $school->name;
            // 内部开发，发送消息通知
            $message = [
                'title' => '学生手动批量升年级成功',
                'content' => '学生手动批量升年级：学校：'. $school_name . '（ID：'. $school_id . '）校区ID：'. $school_campus_id . '年级ID：'. $grade_id . '，学生数量：' . count($student_list),
                'type' => 'info'
            ];
            $messageService = new MessageService();
            $messageService->sendMessage([$request->user()->id], $message);

            return $student_list;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("学生手动批量升年级失败", 500, $e->getMessage());
        }
    }

    // 创建学生班级关系
    protected function createStudentClass(int $studentId, int $classId, string $className, string $schoolYear, string $realName, string $remark)
    {
        StudentClass::create([
            'student_id' => $studentId,
            'class_id' => $classId,
            'class_name' => $className,
            'school_year' => $schoolYear,
            'creator' => $realName,
            'remark' => $remark
        ]);
    }

    // 批量一键升级学生年级
    public function batchUpgradeAll(int $school_id, int $school_campus_id)
    {
        // 获取当前学年
        $current_school_year = getCurrentSchoolYear();
        // 下一学年
        $target_school_year = $current_school_year + 1;

        try {
            DB::beginTransaction();

            // 从当前学年取出目标学生与其当前班级，并映射下一个年级的同名班级
            $rows = DB::table('student_classes as sc')
                ->join('students as s', 's.id', '=', 'sc.student_id')
                ->join('classes as c1', 'c1.id', '=', 'sc.class_id')
                ->leftJoin('classes as c2', function ($join) {
                    $join->on('c2.school_id', '=', 'c1.school_id')
                         ->on('c2.school_campus_id', '=', 'c1.school_campus_id')
                         ->on('c2.class_name', '=', 'c1.class_name')
                         ->on('c2.grade_id', '=', DB::raw('c1.grade_id + 1'));
                })
                ->where('s.school_id', $school_id)
                ->when($school_campus_id, fn($q) => $q->where('s.school_campus_id', $school_campus_id))
                ->where('sc.school_year', $current_school_year)
                ->whereNull('sc.deleted_at')
                ->where('c1.grade_id', '<', 12) // 无下一个年级的跳过
                ->select([
                    'sc.student_id',
                    'c1.class_name',
                    'c1.school_campus_id',
                    'c1.grade_id',
                    DB::raw('c1.grade_id + 1 as next_grade_id'),
                    'c2.id as new_class_id'
                ])
                ->get();

            if ($rows->isEmpty()) {
                return $this->success("未找到当前学年的学生数据，学校ID为：{$school_id}，校区ID为：{$school_campus_id}，当前学年为：{$current_school_year}");
            }

            // 检查是否存在找不到对应新班级的情况
            $missing = $rows->whereNull('new_class_id');
            if ($missing->isNotEmpty()) {
                $first = $missing->first();
                $nextGrade = $first->next_grade_id;
                throw new BusinessException(
                    "批量升级学生年级失败：没有找到对应的新班级，请先创建该班级。学校ID为：{$school_id}，校区ID为：{$first->school_campus_id}，年级ID为：{$nextGrade}，班级名称：{$first->class_name}",
                    500
                );
            }

            $real_name = '系统自动升级';
            $now = now();

            // 组装批量插入的数据
            $insertRows = $rows->map(function ($r) use ($real_name, $target_school_year, $now) {
                return [
                    'student_id' => $r->student_id,
                    'class_id' => $r->new_class_id,
                    'class_name' => $r->class_name,
                    'school_year' => $target_school_year,
                    'creator' => $real_name,
                    'created_at' => $now,
                    'updated_at' => $now,
                    'remark' => '自动批量升级学生年级班级',
                ];
            })->all();

            // 判断批量新增的数据，数据库中是否存在
            $existing = StudentClass::whereIn('student_id', $rows->pluck('student_id')->unique()->values()->all())
                ->where('school_year', $target_school_year)
                ->whereNull('deleted_at')
                ->pluck('student_id')
                ->all();
            if (!empty($existing)) {
                $existingStudents = Student::whereIn('id', $existing)
                    ->pluck('student_name')
                    ->all();
                $existingStudents = implode(',', array_values($existingStudents));
                throw new BusinessException("批量升级学生年级失败：数据库中已存在该学生数据，请勿重复升级。学校ID为：{$school_id}，校区ID为：{$school_campus_id}，已存在的学生：{$existingStudents}", 500);
            }

            foreach (array_chunk($insertRows, 1000) as $chunk) {
                DB::table('student_classes')->insert($chunk);
            }

            DB::commit();

            return $this->success("成功升级 ".count($insertRows)." 名学生到新学年");
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("批量升级学生年级失败：学校ID为：{$school_id}，校区ID为：{$school_campus_id}，" . $e->getMessage(), 500);
        }
    }

    // 修改学生
    public function update(StudentRequest $request, $id)
    {
        $record = Student::find($id);
        if (!$record) {
            $this->throwBusinessException('更新对象不存在');
        }

        $class = Claass::find($request['class_id']);
        if (!$class) {
            $this->throwBusinessException('班级不存在');
        }

        // 校区
        $school_campus_id = $request['school_campus_id'];
        if (!$school_campus_id) {
            $this->throwBusinessException('请选择校区');
        }
        if ($school_campus_id != $class->school_campus_id) {
            $this->throwBusinessException('当前班级与校区不匹配');
        }

        try {
            DB::beginTransaction();

            // 更新学生用户信息
            $real_name = $request->user()->real_name;
            $user = User::find($record->user_id);
            $user->update([
                'real_name' => $request['student_name'],
                'gender' => $request['gender'],
                'updater' => $real_name
            ]);

            // 删除用户原来的角色
            $user->roles()->detach();
            // 保存用户所属角色
            $user->roles()->sync($request['roles']);

            // 更新学生信息
            $record->update([
                'student_name' => $request['student_name'],
//                'gender' => $request['gender'],
                'school_no' => $request['school_no'],
                'updater' => $real_name
            ]);

            // 更新学生班级信息
            $student_class = StudentClass::where('student_id', $record->id)->latest('id')->first();
            $student_class->update([
                'class_id' => $request['class_id'],
                'class_name' => $class->class_name,
                'school_year' => $request['school_year'],
                'updater' => $real_name
            ]);

            DB::commit();

            return $record;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("更新学生信息失败", 500, $e->getMessage());
        }
    }

    public function destroy(Request $request, $id)
    {
        $record = Student::find($id);
        if (!$record) {
            $this->throwBusinessException('删除学生对象不存在');
        }

        try {
            DB::beginTransaction();

            $record->delete();

            // 删除学生对应的用户
            $this->userService->destroy($request, $record->user_id);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("删除学生信息失败", 500, $e->getMessage());
        }
    }

    // Constants moved to App\Constants\SchoolConstants



    /**
 * 获取学生数据，用户学生列表穿梭框使用
 * 返回层级结构：校区->年级->班级->学生
 *
 * @param StudentRequest $request
 * @return array
 */
public function transferData(StudentRequest $request)
{
    // 获取学年
    $school_year = $request->input('school_year') ?? getCurrentSchoolYear();

    // 获取学校ID
    $school_id = $this->classService->getSchoolId($request);
    $school = School::find($school_id);
    $isShanghai = SchoolConstants::isShanghai($school->province);

    // 1. 一次性获取所有校区
    $campuses = SchoolCampus::where('school_id', $school_id)
        ->where('status', 1)
        ->get(['id', 'campus_name', 'type']);

    // 2. 获取所有校区类型对应的年级ID
    $allGradeIds = [];
    foreach ($campuses as $campus) {
        $gradeIds = SchoolConstants::getGradeIdsByType($campus->type, $isShanghai);
        $allGradeIds = array_merge($allGradeIds, $gradeIds);
    }
    $allGradeIds = array_unique($allGradeIds);

    // 3. 一次性获取所有年级
    // $grades = Grade::whereIn('id', $allGradeIds)
    //     ->get(['id', 'grade_name'])
    //     ->keyBy('id');
    // 3. 一次性获取所有年级
    $gradesQuery = Grade::whereIn('id', $allGradeIds);
    if ($isShanghai) {
        // 如果是上海地区，使用别名作为年级名称
        $grades = $gradesQuery->get(['id', 'alisa_name as grade_name'])->keyBy('id');
    } else {
        $grades = $gradesQuery->get(['id', 'grade_name'])->keyBy('id');
    }

    // 4. 一次性获取所有班级
    $classes = Claass::where('school_id', $school_id)
        ->whereIn('school_campus_id', $campuses->pluck('id'))
        ->whereIn('grade_id', $allGradeIds)
        ->get(['id', 'class_name', 'school_campus_id', 'grade_id'])
        ->groupBy(['school_campus_id', 'grade_id']);

    // 5. 一次性获取所有学生及其班级关系
    // 获取所有班级ID（使用集合扁平化以避免三层循环）
    $classIds = $classes->flatten(2)->pluck('id')->all();

    if (empty($classIds)) {
        return [];
    }

    // 在数据库层面获取每个学生的最新班级关系（MySQL 8+ 窗口函数 ROW_NUMBER）
    $sub = DB::table('student_classes as sc')
        ->selectRaw('sc.id, sc.student_id, sc.class_id, ROW_NUMBER() OVER (PARTITION BY sc.student_id ORDER BY sc.id DESC) as rn')
        ->whereIn('sc.class_id', $classIds)
        ->whereNull('sc.deleted_at')
        ->where('sc.school_year', $school_year);

    $studentClassRelations = DB::query()
        ->fromSub($sub, 't')
        ->where('t.rn', 1)
        ->select('t.id as student_class_id', 't.student_id', 't.class_id')
        ->get()
        ->groupBy('class_id');

    // 获取所有相关学生ID
    $studentIds = $studentClassRelations->flatten(1)->pluck('student_id')->unique()->all();

    // 一次性获取所有学生信息
    $students = Student::whereIn('id', $studentIds)
        ->get(['id', 'student_name'])
        ->keyBy('id');

    // 6. 构建结果树
    $result = [];

    foreach ($campuses as $campus) {
        $campusData = [
            'label' => $campus->campus_name,
            'value' => $campus->id,
            'level' => 0,
            'children' => []
        ];

        $gradeIds = SchoolConstants::getGradeIdsByType($campus->type, $isShanghai);
        $hasGrades = false;

        foreach ($gradeIds as $gradeId) {
            if (!isset($grades[$gradeId])) continue;

            $grade = $grades[$gradeId];
            $gradeData = [
                'label' => $grade->grade_name,
                'value' => $grade->id,
                'level' => 1,
                'children' => []
            ];

            // 获取该校区和年级下的所有班级
            $campusGradeClasses = $classes[$campus->id][$grade->id] ?? collect();
            $hasClasses = false;

            foreach ($campusGradeClasses as $class) {
                $classData = [
                    'label' => $class->class_name,
                    'value' => $class->id,
                    'level' => 2,
                    'children' => []
                ];

                // 获取该班级的所有学生
                $classStudentRelations = $studentClassRelations[$class->id] ?? collect();

                foreach ($classStudentRelations as $relation) {
                    $studentId = $relation->student_id;
                    if (isset($students[$studentId])) {
                        $student = $students[$studentId];
                        $classData['children'][] = [
                            'label' => $student->student_name,
                            'value' => $student->id,
                            'student_class_id' => $relation->student_class_id,
                            'level' => 3,
                            'isLeaf' => true
                        ];
                    }
                }

                // 只添加有学生的班级
                if (count($classData['children']) > 0) {
                    $gradeData['children'][] = $classData;
                    $hasClasses = true;
                }
            }

            // 只添加有班级的年级
            if ($hasClasses) {
                $campusData['children'][] = $gradeData;
                $hasGrades = true;
            }
        }

        // 只添加有年级的校区
        if ($hasGrades) {
            $result[] = $campusData;
        }
    }

    return $result;
}

    /**
     * 根据班级名称获取班级ID
     *
     * @param string $class_name
     * @param int $school_id
     * @param int $school_campus_id
     * @param int $grade_id
     * @return int|null
     */
    private function getClassIdByName($class_name, $school_id, $school_campus_id, $grade_id)
    {
        $class = Claass::where('school_id', $school_id)
            ->where('school_campus_id', $school_campus_id)
            ->where('grade_id', $grade_id)
            ->where('class_name', $class_name)
            ->first();

        return $class ? $class->id : null;
    }
}
